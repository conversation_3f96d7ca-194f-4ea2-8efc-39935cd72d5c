import {
  InstancedMesh,
  ShaderMaterial,
  PlaneGeometry,
  Vector2,
  Color,
  AdditiveBlending,
  Vector3,
  FrontSide,
} from "three";
import { Point } from "../types";
import { studyStatusToColor } from "../utils";
import vertexShader from "../shader/haloVert.glsl?raw";
import fragmentShader from "../shader/haloFrag.glsl?raw";
import { Context } from "../ctx";

/**
 * 悬停特效系统 - KsgHover.ts
 *
 * 职责：
 * 1. 为鼠标悬停的节点提供动态视觉反馈
 * 2. 实现扩散的圆环光效动画
 * 3. 支持根据节点状态变化颜色
 * 4. 提供流畅的悬停状态切换
 *
 * 视觉特点：
 * - 多层圆环扩散效果
 * - 渐变透明度动画
 * - 自适应朝向相机
 * - 可配置的动画参数
 */

// 共享的平面几何体，所有悬停特效使用同一个几何体
const geo = new PlaneGeometry(6, 6);

/**
 * 悬停圆环特效类
 * 继承自Three.js的InstancedMesh，支持高效的实例化渲染
 * 使用自定义着色器实现复杂的扩散动画效果
 */
class KsgHoverCircle extends InstancedMesh {
  /** 上一个悬停节点的索引 - 用于避免重复处理 */
  lastIndex: number = -1;

  /**
   * 构造函数 - 初始化悬停特效
   *
   * @param size 特效大小 - 控制圆环的整体尺寸
   */
  constructor(size: number = 6) {
    super(
      geo,
      new ShaderMaterial({
        uniforms: {
          uTime: { value: 0.0 }, // 动画时间进度
          uSpriteSize: { value: new Vector2(size, size) }, // 精灵尺寸
          uSpeed: { value: 0.5 }, // 扩散速度系数
          uMinRadius: { value: 0.5 }, // 最小圆环半径
          uNumRings: { value: 10.0 }, // 圆环数量
          uRingDuration: { value: 2.0 }, // 单个圆环持续时间
          uDelay: { value: 1.0 }, // 圆环间延迟
          uColor: { value: new Color(0xffffff) }, // 基础颜色
        },
        vertexShader,
        fragmentShader,
        transparent: true,
        side: FrontSide,
        depthWrite: true,
        depthTest: true, // 保持深度测试
        blending: AdditiveBlending, // 加法混合创建发光效果
      }),
      1 // 实例数量
    );

    // 设置渲染顺序，确保在适当的层级渲染
    this.renderOrder = 4;
  }

  /**
   * 显示悬停特效并绑定到指定节点
   *
   * 执行流程：
   * 1. 检查是否为重复悬停（优化性能）
   * 2. 重置动画时间
   * 3. 设置特效位置到节点坐标
   * 4. 根据节点状态设置颜色
   * 5. 显示特效并缓存节点索引
   *
   * @param bindPoint 要绑定的悬停节点对象
   */
  display(bindPoint: Point) {
    // 避免重复处理同一个节点
    if (this.lastIndex === bindPoint.index) return;

    // 重置动画时间，确保动画从头开始
    (this.material as ShaderMaterial).uniforms.uTime.value = 0;

    // 设置特效位置与节点坐标一致
    this.position.set(...bindPoint.coordinate);

    // 根据节点学习状态设置特效颜色
    (this.material as ShaderMaterial).uniforms.uColor.value =
      studyStatusToColor(bindPoint.status);

    // 显示特效
    this.visible = true;

    // 缓存当前悬停节点的索引
    this.lastIndex = bindPoint.index!;
  }

  /**
   * 隐藏悬停特效
   *
   * 在鼠标移出节点或切换悬停目标时调用
   */
  hide() {
    this.visible = false;
    this.lastIndex = -1;
  }

  /**
   * 动画更新函数 - 每帧调用
   *
   * 职责：
   * 1. 推进扩散动画的时间进度
   * 2. 计算特效面向相机的朝向
   * 3. 只有在可见时才执行，优化性能
   *
   * @param ctx 上下文对象 - 包含相机和控制器信息
   * @param delta 时间增量 - 两帧之间的时间差
   */
  update(ctx: Partial<Context>, delta: number = 0.01) {
    // 只有在特效可见时才执行更新
    if (!this.visible) return;

    // 推进着色器中的时间变量，驱动扩散动画
    (this.material as ShaderMaterial).uniforms.uTime.value += delta;

    // 计算从相机到特效的方向向量
    const v3 = new Vector3(
      ctx.camera!.position.x,
      ctx.camera!.position.y,
      ctx.camera!.position.z
    );

    // 获取特效的世界坐标位置
    const crtWorldPosition = new Vector3();
    this.getWorldPosition(crtWorldPosition);

    // 计算朝向相机的方向
    v3.sub(ctx.controls!.target).normalize();

    // 让特效面板始终朝向相机，确保最佳视觉效果
    this.lookAt(
      new Vector3(
        v3.x / 2 + crtWorldPosition.x,
        v3.y / 2 + crtWorldPosition.y,
        v3.z / 2 + crtWorldPosition.z
      )
    );
  }

  /**
   * 释放内存资源
   *
   * 在组件销毁时调用，防止内存泄漏
   */
  free() {
    this.geometry.dispose();
    (this.material as ShaderMaterial).dispose();
  }
}

// 创建全局唯一的悬停特效实例
const ksgHover = new KsgHoverCircle();
ksgHover.visible = false; // 初始状态为隐藏

export default ksgHover;
