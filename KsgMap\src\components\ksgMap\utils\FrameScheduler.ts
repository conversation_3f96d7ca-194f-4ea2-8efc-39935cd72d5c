/**
 * 分帧处理调度器
 * 用于将大量计算任务分散到多个渲染帧中执行，避免阻塞主线程
 * 适用于处理大数据集、复杂计算等可能导致页面卡顿的操作
 */
export default class FrameScheduler {
  /** 任务队列，存储待执行的任务函数 */
  private taskQueue: (() => boolean)[] = []; // 函数返回true表示最后一个任务执行完成，false表示继续执行

  /** 标记调度器是否正在运行 */
  private isRunning = false;

  /** 所有任务完成后的回调函数 */
  completedCallback: () => void = () => {};
  /**
   * 添加任务到队列
   * @param task 待执行的任务函数
   *             - 返回 true: 表示所有任务已完成，停止调度
   *             - 返回 false: 表示还有任务需要继续执行
   */
  addTask(task: () => boolean) {
    this.taskQueue.push(task);
    // 如果调度器当前没有运行，立即开始执行任务
    if (!this.isRunning) {
      this.runNextTask();
    }
  }

  /**
   * 分帧计算函数，此函数在渲染帧中调用，调用一次则执行一次处理任务
   * 使用 requestAnimationFrame 确保任务在下一个渲染帧执行，避免阻塞当前帧
   */
  runNextTask() {
    this.isRunning = true;

    // 在下一个渲染帧执行任务，确保不阻塞当前帧的渲染
    requestAnimationFrame(() => {
      // 从队列头部取出一个任务
      const task = this.taskQueue.shift();

      if (task) {
        // 执行任务并获取完成状态
        const isCompleted = task();

        if (isCompleted) {
          // 任务执行完成，停止调度并触发完成回调
          this.isRunning = false;
          this.completedCallback();
          return;
        } // 执行完成触发回调

        // 任务未完成，继续执行下一个任务
        this.runNextTask();
      } else {
        // 队列为空，停止调度
        this.isRunning = false;
      }
    });
  }
  /**
   * 清空任务队列
   * 立即停止所有未执行的任务，重置调度器状态
   */
  clearTasks() {
    this.taskQueue = [];
    this.isRunning = false;
  }

  /**
   * 设置任务完成回调函数
   * @param callback 当所有任务执行完成时触发的回调函数
   */
  onCompleted(callback: () => void) {
    if (!callback) return;
    this.completedCallback = callback;
  }
}
