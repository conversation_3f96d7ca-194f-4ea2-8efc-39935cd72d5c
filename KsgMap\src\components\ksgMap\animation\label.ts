import TWEEN from "@tweenjs/tween.js";
import { CSS2DObject } from "three/examples/jsm/Addons.js";

//hover时刻显示label
export function labelEnter(labelObject: CSS2DObject, duration: number = 150) {
  labelObject.element.style.opacity = "0";
  labelObject.position.set(0, 0.4, 0);
  return new Promise((resolve) => {
    new TWEEN.Tween({ x: 0, y: 0.4, z: 0, opacity: 0 })
      .to({ x: 0, y: 0, z: 0, opacity: 1 }, duration)
      .onComplete(resolve)
      .onUpdate(({ x, y, z, opacity }) => {
        labelObject.position.set(x, y, z);
        labelObject.element.style.opacity = opacity.toString();
      })
      .start();
  });
}

//hover离开后隐藏label
export function labelLeave(labelObject: CSS2DObject, duration: number = 150) {
  labelObject.element.style.opacity = "1";
  labelObject.position.set(0, 0, 0);
  return new Promise((resolve) => {
    new TWEEN.Tween({ x: 0, y: 0, z: 0, opacity: 1 })
      .onUpdate(({ x, y, z, opacity }) => {
        labelObject.position.set(x, y, z);
        labelObject.element.style.opacity = opacity.toString();
      })
      .onComplete(() => {
        resolve(labelObject);
      })
      .to({ x: 0, y: -0.4, z: 0, opacity: 0 }, duration)
      .start();
  });
}

// 根标签初始动画
export function rootLabelAnimation(
  labelObject: CSS2DObject,
  duration: number = 200,
  mode: "in" | "out" = "in"
) {
  return new Promise((resolve) => {
    new TWEEN.Tween({ opacity: mode === "in" ? 0 : 1 })
      .to({ opacity: mode === "in" ? 1 : 0 }, duration)
      .onUpdate(({ opacity }) => {
        labelObject.element.style.opacity = opacity.toString();
      })
      .onComplete(resolve)
      .start();
  });
}
