import { Raycaster, Vector2, MOUS<PERSON> } from "three";
import type { Point, Size } from "../types";
import KsgPoint from "../core/KsgPoints";
import { type Context } from "../ctx";

/**
 * 创建点击事件处理器
 * 使用Three.js的射线投射检测用户点击的3D点对象
 * @param elSize 容器元素的尺寸信息
 * @param ctx 应用上下文，包含相机、网格等对象
 * @param clickedPointCallback 点击点对象时的回调函数
 * @returns 包含事件处理、清理和尺寸更新方法的对象
 */
export default function createClickEvent(
  elSize: Size,
  ctx: Partial<Context>,
  clickedPointCallback: (data: Point) => void
) {
  // 容器的宽度和高度，用于坐标转换
  let width: number | null = elSize.width;
  let height: number | null = elSize.height;
  
  // 射线投射器，用于检测鼠标点击的3D对象
  let raycaster: null | Raycaster = new Raycaster();

  /**
   * 清空闭包造成的内存泄漏
   * 将所有引用设置为null，释放内存
   */
  function clear() {
    width = null;
    height = null;
    raycaster = null;
  }
  
  /**
   * 更新容器尺寸
   * 当容器大小改变时需要调用此方法更新坐标转换参数
   * @param w 新的宽度
   * @param h 新的高度
   */
  function updateSize(w: number, h: number) {
    width = w;
    height = h;
  }
  /**
   * 鼠标点击事件处理函数
   * 通过射线投射检测点击的3D点对象，并触发回调
   * @param e 鼠标事件对象
   */
  function event(e: MouseEvent) {
    // 检查点网格是否存在
    if (!ctx.pointsMesh) return;
    
    const { offsetX, clientX, offsetY, clientY, button } = e;
    
    // 如果点击的是标签或者不是左键点击，则忽略
    if (isClickedLabel(clientX, clientY) || button !== MOUSE.LEFT) return;
    
    // 将屏幕坐标转换为标准化设备坐标(-1 到 1)
    raycaster!.setFromCamera(
      new Vector2((offsetX / width!) * 2 - 1, -(offsetY / height!) * 2 + 1),
      ctx.camera!
    );
    
    // 检测与点网格的交集
    const intersects = raycaster!.intersectObject(ctx.pointsMesh);

    let fistPointIndex: number | null = null;
    if (intersects.length > 0) {
      // 获取第一个相交点的索引
      fistPointIndex = intersects[0].index as number;
    }
    
    // 检查是否有相交对象且距离在允许范围内
    if (intersects[0]?.object && intersects[0].distance <= ctx.maxDistance!) {
      // 获取点击的点对象ID
      const id = (intersects[0].object as KsgPoint).getPointData(
        fistPointIndex!
      )!.id;
      
      // 触发点击回调，传递完整的点数据
      clickedPointCallback(
        ctx.graph?.getPointById(id)!
      );
    }
  }
  // 返回公开的方法接口
  return {
    clear,        // 清理方法
    event,        // 事件处理方法
    updateSize,   // 尺寸更新方法
  };
}

/**
 * 检测点击目标是否为标签元素
 * 避免点击标签时触发点对象的点击事件
 * @param clientX 鼠标点击的屏幕X坐标
 * @param clientY 鼠标点击的屏幕Y坐标
 * @returns true: 点击到标签元素, false: 点击到其他地方
 */
function isClickedLabel(clientX: number, clientY: number): boolean {
  // 获取指定坐标位置的DOM元素
  const e = document.elementFromPoint(clientX, clientY);
  const id = findValidateParentNode(e);
  if (id) return true;
  return false;
}

/**
 * 递归查找有效的父节点
 * 向上遍历DOM树，查找具有特定类名的标签元素
 * @param ele 要检查的DOM节点
 * @returns 如果找到标签元素则返回其ID，否则返回null
 */
function findValidateParentNode(ele: Node | null) {
  if (!ele) return null;
  
  // 检查当前节点是否为DIV且具有css2d-label类名
  if (ele.nodeName === "DIV" && (ele as Element).className === "css2d-label")
    return (ele as Element).getAttribute("id");
    
  // 递归检查父节点
  return findValidateParentNode(ele.parentNode);
}
