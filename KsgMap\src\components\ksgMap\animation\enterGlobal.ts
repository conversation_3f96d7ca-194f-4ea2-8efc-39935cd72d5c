import { Tween, Easing } from "@tweenjs/tween.js";
import { KsgControls } from "../core/KsgControls";
import type { Line2 } from "three/examples/jsm/lines/webgpu/Line2.js";

/**
 *
 * @param {OrbitControls } controls 相机控制器
 * @param {[number,number,number]} to 目标位置
 */
export function enterGlobalAnimation(
  controls: KsgControls,
  to: [number, number, number]
) {
  return new Promise((resolve) => {
    new Tween({
      x: controls.object.position.x,
      y: controls.object.position.y,
      z: controls.object.position.z,
      targetX: controls.target.x,
      targetY: controls.target.y,
      targetZ: controls.target.z,
    })
      .to(
        {
          x: to[0],
          y: to[1] + 5,
          z: to[2],
          targetX: 0,
          targetY: to[1],
          targetZ: 0,
        },
        400
      )
      .onUpdate(({ x, y, z, targetY, targetX, targetZ }) => {
        controls.object.position.set(x, y, z);
        controls.target.set(targetX, targetY, targetZ);
      })
      .easing(Easing.Quartic.InOut)
      .start()
      .onComplete(resolve);
  });
}

/**
 * 清除边动画
 * @param {Line2} line 需要清除的边
 */
export function clearLineAnimation(line: Line2): Promise<Line2> {
  return new Promise((resolve) => {
    new Tween({ opacity: line.material.opacity })
      .to({ opacity: 0 }, 1000)
      .onUpdate(({ opacity }) => {
        line.material.opacity = opacity;
      })
      .easing(Easing.Quartic.InOut)
      .start()
      .onComplete(() => resolve(line));
  });
}
