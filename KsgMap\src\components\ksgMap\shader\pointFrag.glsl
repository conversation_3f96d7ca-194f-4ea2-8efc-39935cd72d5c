varying vec3 vColor;
varying float vOpacity;
uniform sampler2D map;       // 颜色贴图
uniform sampler2D alphaMap;  // 透明度贴图
uniform vec2 offset;  //纹理偏移量
uniform float uTime;  //动画时间
// uniform bool isBreathAni;  //是否开启呼吸效果

varying vec2 vUv;
varying float vCustomRandom; //呼吸时的随机相位
varying float vIsBreathAni;

void main() {
	
    vec4 texColor = texture2D(map, gl_PointCoord + offset);     // 颜色贴图
    float alpha = texture2D(alphaMap, gl_PointCoord + offset).a; // Alpha 贴图（使用 a 通道）
 
    // 是否开启呼吸效果
    if(vIsBreathAni == 1.0){
        float finalOpacity = sin(uTime +  vCustomRandom) * 0.4  + 0.6; 
        vec4 finalColor = vec4(vColor * texColor.rgb, finalOpacity);
         // 透明度剔除（去除黑色背景）
        gl_FragColor = finalColor;
    }else{
        vec4 finalColor = vec4(vColor * texColor.rgb, texColor.a * alpha * vOpacity);
        // 透明度剔除（去除黑色背景）
        gl_FragColor = finalColor;
    }
    
}
