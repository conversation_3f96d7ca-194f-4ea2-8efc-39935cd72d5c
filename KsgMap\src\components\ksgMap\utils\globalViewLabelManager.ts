/**
 * 全局视角下的Label管理器
 * 负责在自动旋转过程中智能显示/隐藏节点标签
 */

import { Vector3, Frustum, Matrix4, PerspectiveCamera } from 'three';
import type { Point } from '../types';
import { KsgLabel } from '../core/KsgLabel';
import ctx from '../ctx';

/**
 * 全局视角Label显示配置
 */
interface GlobalLabelConfig {
  /** 最大显示距离 - 超过此距离的节点不显示标签 */
  maxDistance: number;
  /** 最大深度范围 - 相对于target的前景深度 */
  maxDepth: number;
  /** 视野角度系数 - 控制视锥体检测的严格程度 */
  viewAngleFactor: number;
  /** 更新频率控制 - 每N帧检测一次，优化性能 */
  updateInterval: number;
}

/**
 * 默认配置
 */
const DEFAULT_CONFIG: GlobalLabelConfig = {
  maxDistance: 80,        // 最大显示距离
  maxDepth: 50,          // 最大深度范围
  viewAngleFactor: 0.8,  // 视野角度系数
  updateInterval: 3,     // 每3帧检测一次
};

/**
 * 全局视角Label管理器类
 */
export class GlobalViewLabelManager {
  /** 配置参数 */
  private config: GlobalLabelConfig;
  /** 当前显示的标签Map */
  private activeLabels: Map<string, KsgLabel> = new Map();
  /** 视锥体对象 - 用于检测节点是否在相机视野内 */
  private frustum: Frustum = new Frustum();
  /** 帧计数器 - 用于控制更新频率 */
  private frameCount: number = 0;
  /** 是否启用 */
  private enabled: boolean = false;

  constructor(config: Partial<GlobalLabelConfig> = {}) {
    this.config = { ...DEFAULT_CONFIG, ...config };
  }

  /**
   * 启用全局标签管理
   */
  enable() {
    this.enabled = true;
    console.log('GlobalViewLabelManager enabled');
  }

  /**
   * 禁用全局标签管理并清理所有标签
   */
  disable() {
    this.enabled = false;
    this.clearAllLabels();
    console.log('GlobalViewLabelManager disabled');
  }

  /**
   * 每帧更新函数 - 在渲染循环中调用
   * 检测哪些节点应该显示标签，哪些应该隐藏
   */
  update() {
    if (!this.enabled || !ctx.camera || !ctx.pointsMesh || !ctx.graph) return;

    // 控制更新频率，避免每帧都进行复杂计算
    this.frameCount++;
    if (this.frameCount % this.config.updateInterval !== 0) return;

    // 更新视锥体
    this.updateFrustum();

    // 获取所有节点
    const allPoints = Array.from(ctx.graph.pointsData.values());
    
    // 检测应该显示的节点
    const shouldShowPoints = this.detectVisiblePoints(allPoints);
    
    // 更新标签显示状态
    this.updateLabelsDisplay(shouldShowPoints);
  }

  /**
   * 更新相机视锥体
   */
  private updateFrustum() {
    const camera = ctx.camera!;
    const projectionMatrix = new Matrix4().multiplyMatrices(
      camera.projectionMatrix,
      camera.matrixWorldInverse
    );
    this.frustum.setFromProjectionMatrix(projectionMatrix);
  }

  /**
   * 检测应该显示标签的节点
   * @param points 所有节点数组
   * @returns 应该显示标签的节点数组
   */
  private detectVisiblePoints(points: Point[]): Point[] {
    const camera = ctx.camera!;
    const target = ctx.controls!.target;
    const cameraPosition = camera.position;
    
    return points.filter(point => {
      const pointPosition = new Vector3(...point.coordinate);
      
      // 条件1: 视锥体检测 - 节点是否在相机视野内
      if (!this.frustum.containsPoint(pointPosition)) {
        return false;
      }

      // 条件2: 距离检测 - 节点是否在合理的显示距离内
      const distanceToCamera = pointPosition.distanceTo(cameraPosition);
      if (distanceToCamera > this.config.maxDistance) {
        return false;
      }

      // 条件3: 深度检测 - 节点是否在前景区域
      // 计算节点相对于target的深度
      const cameraToTarget = target.clone().sub(cameraPosition);
      const cameraToPoint = pointPosition.clone().sub(cameraPosition);
      
      // 使用点积计算深度投影
      const depth = cameraToPoint.dot(cameraToTarget.normalize());
      const targetDepth = cameraToTarget.length();
      
      // 节点应该在target前方一定范围内
      if (depth > targetDepth + this.config.maxDepth) {
        return false;
      }

      // 条件4: 角度检测 - 节点是否在相机朝向的合理角度内
      const angleToPoint = cameraToTarget.angleTo(cameraToPoint);
      const maxAngle = (camera.fov * Math.PI / 180) * this.config.viewAngleFactor;
      
      if (angleToPoint > maxAngle) {
        return false;
      }

      return true;
    });
  }

  /**
   * 更新标签显示状态
   * @param shouldShowPoints 应该显示标签的节点数组
   */
  private updateLabelsDisplay(shouldShowPoints: Point[]) {
    const shouldShowIds = new Set(shouldShowPoints.map(p => p.id));
    
    // 隐藏不再需要显示的标签
    for (const [pointId, label] of this.activeLabels) {
      if (!shouldShowIds.has(pointId)) {
        this.hideLabel(pointId, label);
      }
    }
    
    // 显示新需要显示的标签
    for (const point of shouldShowPoints) {
      if (!this.activeLabels.has(point.id)) {
        this.showLabel(point);
      }
    }
  }

  /**
   * 显示节点标签
   * @param point 节点对象
   */
  private showLabel(point: Point) {
    // 创建新的标签实例
    const label = new KsgLabel();
    
    // 显示标签
    label.display(point);
    
    // 添加到场景
    ctx.viewGroup?.add(label);
    
    // 记录到活跃标签Map
    this.activeLabels.set(point.id, label);
    
    console.log(`Show label for point: ${point.name}`);
  }

  /**
   * 隐藏节点标签
   * @param pointId 节点ID
   * @param label 标签对象
   */
  private hideLabel(pointId: string, label: KsgLabel) {
    // 隐藏标签
    label.hide();
    
    // 从场景移除
    ctx.viewGroup?.remove(label);
    
    // 从活跃标签Map移除
    this.activeLabels.delete(pointId);
    
    console.log(`Hide label for point: ${pointId}`);
  }

  /**
   * 清理所有标签
   */
  private clearAllLabels() {
    for (const [pointId, label] of this.activeLabels) {
      this.hideLabel(pointId, label);
    }
    this.activeLabels.clear();
  }

  /**
   * 更新配置
   * @param newConfig 新的配置参数
   */
  updateConfig(newConfig: Partial<GlobalLabelConfig>) {
    this.config = { ...this.config, ...newConfig };
    console.log('GlobalViewLabelManager config updated:', this.config);
  }

  /**
   * 获取当前显示的标签数量
   */
  getActiveLabelCount(): number {
    return this.activeLabels.size;
  }

  /**
   * 获取当前配置
   */
  getConfig(): GlobalLabelConfig {
    return { ...this.config };
  }
}

// 创建全局实例
export const globalLabelManager = new GlobalViewLabelManager();

/**
 * 使用示例和配置说明
 *
 * 1. 基础使用：
 * ```typescript
 * // 启用全局标签管理（通常在进入全局视图时调用）
 * globalLabelManager.enable();
 *
 * // 禁用全局标签管理（通常在退出全局视图时调用）
 * globalLabelManager.disable();
 * ```
 *
 * 2. 自定义配置：
 * ```typescript
 * // 调整显示距离和检测参数
 * globalLabelManager.updateConfig({
 *   maxDistance: 100,      // 增加最大显示距离
 *   maxDepth: 60,         // 增加深度范围
 *   viewAngleFactor: 0.9, // 放宽视角限制
 *   updateInterval: 2,    // 提高检测频率
 * });
 * ```
 *
 * 3. 监控状态：
 * ```typescript
 * // 获取当前显示的标签数量
 * const labelCount = globalLabelManager.getActiveLabelCount();
 * console.log(`当前显示 ${labelCount} 个标签`);
 *
 * // 获取当前配置
 * const config = globalLabelManager.getConfig();
 * console.log('当前配置:', config);
 * ```
 *
 * 4. 性能优化建议：
 * - 在节点数量较多时，适当增加 updateInterval 减少检测频率
 * - 根据场景调整 maxDistance 和 maxDepth 参数
 * - 在不需要时及时调用 disable() 释放资源
 */
