# KsgMap Label显示机制详解

## 概述

KsgMap中的Label显示系统是一个基于Three.js CSS2DRenderer的智能标签系统，支持两种主要的显示模式：
1. **Hover Label** - 鼠标悬停时临时显示的标签
2. **Focus Label** - 点击节点后持续显示的聚焦标签

## 核心架构

### 1. 标签类结构

```typescript
export class KsgLabel extends CSS2DObject {
  labelWidth: number = 0;      // 标签宽度
  labelHeight: number = 0;     // 标签高度  
  point: Point | null = null;  // 当前绑定的节点
  lastIndex: number | null = null; // 上次显示的节点索引
  offset: { x: number; y: number }; // 标签偏移位置
}
```

### 2. 全局标签实例

系统创建了两个全局标签实例：
- `hoverLabel` - 悬停标签实例
- `focusLabel` - 聚焦标签实例

## Hover显示机制

### 实现流程

```mermaid
sequenceDiagram
    participant User as 用户
    participant Event as 事件系统
    participant Raycaster as 射线投射
    participant HoverLabel as 悬停标签
    participant Animation as 动画系统

    User->>Event: 鼠标移动
    Event->>Raycaster: 坐标转换与射线检测
    Raycaster->>Event: 返回相交节点
    Event->>HoverLabel: 调用display()显示标签
    HoverLabel->>Animation: 执行进入动画
    Animation-->>User: 标签淡入显示
    
    User->>Event: 鼠标移出
    Event->>HoverLabel: 调用hide()隐藏标签
    HoverLabel->>Animation: 执行离开动画
    Animation-->>User: 标签淡出隐藏
```

### 核心代码实现

#### 1. 悬停事件检测 (`hoverObjectEvent.ts`)

<augment_code_snippet path="KsgMap/src/components/ksgMap/utils/hoverObjectEvent.ts" mode="EXCERPT">
````typescript
function event(e: MouseEvent) {
  // 检查点网格是否存在，以及控制器是否正在操作
  if (!ctx.pointsMesh || ctx.controls?.isControls) return;
  
  const { offsetX, offsetY } = e;
  
  // 将屏幕坐标转换为标准化设备坐标(-1到1)
  raycaster!.setFromCamera(
    new THREE.Vector2(
      (offsetX / width!) * 2 - 1,
      -(offsetY / height!) * 2 + 1
    ),
    ctx.camera!
  );
  
  // 检测与点网格的交集
  const intersects = raycaster!.intersectObject(ctx.pointsMesh);
````
</augment_code_snippet>

#### 2. 悬停回调处理 (`event.ts`)

<augment_code_snippet path="KsgMap/src/components/ksgMap/config/event.ts" mode="EXCERPT">
````typescript
// 鼠标悬停到节点时的回调函数
(point: Point) => {
  // 获取节点在屏幕上的2D坐标
  const pointDnc = ctx.pointsMesh?.getWorldP(
    point.index!,
    ctx.camera!,
    wrapperElSize.width * window.devicePixelRatio,
    wrapperElSize.height * window.devicePixelRatio
  )!;

  // 显示悬停光圈效果
  ksgHover.display(point);

  // 显示悬停标签
  hoverLabel.display(point, {
    viewRange: ctx.viewRange!,
    dnc: pointDnc,
  });

  // 改变鼠标样式为指针
  document.body.style.cursor = "pointer";
}
````
</augment_code_snippet>

#### 3. 标签显示逻辑 (`KsgLabel.ts`)

<augment_code_snippet path="KsgMap/src/components/ksgMap/core/KsgLabel.ts" mode="EXCERPT">
````typescript
display(point: Point, option?: { viewRange: ViewRange; dnc: { x: number; y: number } }) {
  // 避免重复显示同一个节点
  if (this.lastIndex === point.index) return;
  
  // 绑定节点数据
  this.point = point;
  
  // 设置标签HTML内容
  this.element.innerHTML = `<div class='css2d-label-inner'>${reFormateTitle(point.name)}</div>`;
  
  // 设置3D位置
  this.position.set(...point.coordinate);
  
  // 渲染MathJax数学公式
  renderMathJax(this.element);
  
  // 计算并设置最佳显示位置
  if (option) {
    this.setPosition(option);
  }
  
  this.visible = true;
  this.lastIndex = point.index;
}
````
</augment_code_snippet>

## 点击持续显示机制

### 实现流程

```mermaid
sequenceDiagram
    participant User as 用户
    participant ClickEvent as 点击事件
    participant FocusSystem as 聚焦系统
    participant FocusLabel as 聚焦标签
    participant Animation as 动画系统

    User->>ClickEvent: 点击节点
    ClickEvent->>FocusSystem: 调用handleEnterFocus()
    FocusSystem->>FocusSystem: 构建聚焦数据
    FocusSystem->>Animation: 执行相机移动动画
    Animation->>FocusLabel: 显示聚焦标签
    FocusLabel-->>User: 标签持续显示
    
    Note over FocusLabel: 标签会一直显示直到：<br/>1. 点击其他节点<br/>2. 退出聚焦模式<br/>3. 进入全局视图
```

### 核心代码实现

#### 1. 点击事件检测 (`clickPointEvent.ts`)

<augment_code_snippet path="KsgMap/src/components/ksgMap/utils/clickPointEvent.ts" mode="EXCERPT">
````typescript
function event(e: MouseEvent) {
  if (!ctx.pointsMesh) return;
  
  const { offsetX, clientX, offsetY, clientY, button } = e;
  
  // 如果点击的是标签或者不是左键点击，则忽略
  if (isClickedLabel(clientX, clientY) || button !== MOUSE.LEFT) return;
  
  // 射线检测
  raycaster!.setFromCamera(
    new Vector2((offsetX / width!) * 2 - 1, -(offsetY / height!) * 2 + 1),
    ctx.camera!
  );
  
  const intersects = raycaster!.intersectObject(ctx.pointsMesh);
  
  if (intersects[0]?.object && intersects[0].distance <= ctx.maxDistance!) {
    const id = (intersects[0].object as KsgPoint).getPointData(fistPointIndex!)!.id;
    clickedPointCallback(ctx.graph?.getPointById(id)!);
  }
}
````
</augment_code_snippet>

#### 2. 聚焦处理逻辑 (`event.ts`)

<augment_code_snippet path="KsgMap/src/components/ksgMap/config/event.ts" mode="EXCERPT">
````typescript
// 点击节点进入聚焦模式的回调
(point: Point) => {
  // 隐藏悬停相关的UI元素
  enableHover = false;
  ksgHover.hide();
  hoverLabel.hide();
  focusLabel.hide();
  
  // 清空之前的动画
  TWEEN.removeAll();
  
  // 显示聚焦外壳效果
  focusCrust.display(point);
  
  // 切换节点为聚焦状态
  ctx.pointsMesh?.toggleFocus(point.index!);
  
  // 执行聚焦处理
  handleEnterFocus(point).then(() => {
    enableHover = true;
  });
}
````
</augment_code_snippet>

#### 3. 聚焦标签显示 (`renderData.ts`)

<augment_code_snippet path="KsgMap/src/components/ksgMap/core/renderData.ts" mode="EXCERPT">
````typescript
export function renderFocusData(focusInfo: FocusData): Promise<void> {
  // 显示聚焦外壳效果
  focusCrust.display(focusInfo.focusPoint);
  ctx.pointsMesh?.toggleFocus(focusInfo.focusPoint.index!);
  
  // 执行视角移动动画
  return viewMoveAnimation(focusInfo.focusPoint.coordinate, ctx.camera!)
    .then(() =>
      // 视角移动完成后显示聚焦标签
      focusLabel.display(focusInfo.focusPoint)
    )
    .then(() => {
      // 重新启用控制器
      ctx.controls!.enabled = true;
      // ... 其他处理逻辑
    });
}
````
</augment_code_snippet>

## 标签动画系统

### 动画类型

1. **进入动画** (`labelEnter`)
   - 透明度：0 → 1
   - 位置：Y轴向上偏移0.4单位后回到原位
   - 持续时间：150ms

2. **离开动画** (`labelLeave`)  
   - 透明度：1 → 0
   - 位置：Y轴向下偏移0.4单位
   - 持续时间：150ms

### 动画实现

<augment_code_snippet path="KsgMap/src/components/ksgMap/animation/label.ts" mode="EXCERPT">
````typescript
// hover时显示label动画
export function labelEnter(labelObject: CSS2DObject, duration: number = 150) {
  labelObject.element.style.opacity = "0";
  labelObject.position.set(0, 0.4, 0);
  
  return new Promise((resolve) => {
    new TWEEN.Tween({ x: 0, y: 0.4, z: 0, opacity: 0 })
      .to({ x: 0, y: 0, z: 0, opacity: 1 }, duration)
      .onUpdate(({ x, y, z, opacity }) => {
        labelObject.position.set(x, y, z);
        labelObject.element.style.opacity = opacity.toString();
      })
      .start();
  });
}
````
</augment_code_snippet>

## 智能定位算法

### 边界检测与位置优化

标签系统具备智能定位功能，能够：
1. 检测视口边界，避免标签溢出
2. 动态调整标签位置和锚点
3. 考虑设备像素比进行精确定位

### 距离控制显示

<augment_code_snippet path="KsgMap/src/components/ksgMap/core/KsgLabel.ts" mode="EXCERPT">
````typescript
// 根据相机距离控制标签显示
distanceShow(show: boolean) {
  this.element.style.setProperty(
    "--animation",
    show ? "label-enter" : "label-leave"
  );
}
````
</augment_code_snippet>

## 总结

KsgMap的Label显示机制通过以下关键技术实现：

1. **射线投射检测** - 精确识别用户交互的节点
2. **双标签系统** - 分离悬停和聚焦状态的标签管理
3. **智能定位算法** - 确保标签始终在可视范围内
4. **平滑动画过渡** - 提供良好的用户体验
5. **状态管理** - 协调不同交互模式下的标签显示

这套机制确保了用户在浏览知识图谱时能够获得直观、流畅的标签显示体验。
