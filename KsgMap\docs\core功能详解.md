# KsgMap 核心模块功能详解

## 概述

KsgMap 的 `core` 文件夹包含了知识图谱可视化的核心功能模块。每个模块负责特定的功能领域，协同工作构建了完整的3D知识图谱交互体验。

## 核心模块架构

```
core/
├── 数据处理层
│   ├── loadData.ts        # 数据加载器
│   ├── renderData.ts      # 渲染数据管理
│   └── KsgGraph.ts        # 图计算引擎
├── 渲染层
│   ├── KsgPoints.ts       # 节点渲染器
│   ├── KsgLine.ts         # 连线渲染器
│   ├── focusCrust.ts      # 聚焦装饰
│   └── KsgHover.ts        # 悬停特效
├── 交互层
│   ├── KsgControls.ts     # 相机控制器
│   ├── KsgLabel.ts        # 标签系统
│   ├── enterFocus.ts      # 聚焦控制
│   └── enterGlobalView.ts # 全局视图
```

## 详细模块功能

### 1. 数据处理层

#### 1.1 KsgGraph.ts - 图计算引擎 ⭐️⭐️⭐️⭐️⭐️
**核心职责：**
- DAG（有向无环图）结构分析
- BFS层级计算算法
- 3D空间坐标布局
- 增量更新支持

**关键特性：**
- 异步计算，不阻塞UI线程
- 同心圆分布算法
- 差异数据管理
- 高性能的空间索引

**使用场景：**
```typescript
// 初始化图计算
const graph = new KsgGraph(pointsData);

// 增量加载
const diffData = await graph.loadMore(newPoints);

// 查询节点
const point = graph.getPointById('node-id');
```

#### 1.2 loadData.ts - 数据加载器 ⭐️⭐️⭐️⭐️
**核心职责：**
- 初始数据加载流程控制
- 增量数据懒加载
- 渲染模式分发
- 系统状态初始化

**工作流程：**
```
原始数据 → 图计算 → 异步等待 → 渲染分发 → 用户交互
```

#### 1.3 renderData.ts - 渲染数据管理 ⭐️⭐️⭐️⭐️
**核心职责：**
- 管理不同渲染模式的数据流
- 控制节点和连线的动画序列
- 处理增量渲染的差异数据
- 协调多个渲染组件的协作

### 2. 渲染层

#### 2.1 KsgPoints.ts - 节点渲染器 ⭐️⭐️⭐️⭐️⭐️
**核心职责：**
- 高性能批量节点渲染
- GPU加速的着色器系统
- 动态属性更新（颜色、大小、透明度）
- 呼吸动画和特效支持

**技术亮点：**
- BufferGeometry优化内存使用
- 自定义GLSL着色器
- 实例化渲染技术
- 加法混合发光效果

**性能优化：**
- 预分配顶点缓冲区
- 批量属性更新
- 视锥剔除支持
- 动态LOD系统

#### 2.2 KsgLine.ts - 连线渲染器 ⭐️⭐️⭐️⭐️
**核心职责：**
- 节点间连接线渲染
- 流光动画效果
- 动态颜色插值
- 连线的增减管理

**视觉特效：**
- 着色器驱动的流光效果
- 顶点颜色平滑过渡
- 透明度动画支持
- 随机/同步流光模式

#### 2.3 focusCrust.ts - 聚焦装饰 ⭐️⭐️⭐️
**核心职责：**
- 聚焦节点的视觉装饰
- 动态纹理动画
- 状态色彩映射
- 3D外壳渲染

#### 2.4 KsgHover.ts - 悬停特效 ⭐️⭐️⭐️
**核心职责：**
- 鼠标悬停的视觉反馈
- 扩散圆环动画
- 面向相机的朝向控制
- 实时颜色响应

### 3. 交互层

#### 3.1 KsgControls.ts - 相机控制器 ⭐️⭐️⭐️⭐️⭐️
**核心职责：**
- 3D场景相机控制
- 多输入设备支持（鼠标、触摸、键盘）
- 球面坐标系统
- 平滑阻尼系统

**控制模式：**
- 知识点层模式：标准节点浏览
- 领域层模式：宏观领域控制

**交互特性：**
- 旋转、缩放、平移
- Y轴范围限制
- 自动旋转支持
- 控制状态检测

#### 3.2 KsgLabel.ts - 标签系统 ⭐️⭐️⭐️⭐️
**核心职责：**
- 2D HTML标签渲染
- 智能边界检测定位
- MathJax数学公式支持
- 动画过渡效果

**智能定位算法：**
```
边界检测 → 位置计算 → 锚点调整 → CSS样式应用
```

#### 3.3 enterFocus.ts - 聚焦控制 ⭐️⭐️⭐️⭐️
**核心职责：**
- 节点聚焦状态管理
- 子图构建和渲染
- 历史栈管理
- 聚焦动画控制

**工作流程：**
```
点击节点 → 验证有效性 → 构建子图 → 更新历史 → 执行动画
```

#### 3.4 enterGlobalView.ts - 全局视图 ⭐️⭐️⭐️
**核心职责：**
- 全局视图模式切换
- 清理聚焦状态
- 启用呼吸动画
- 视角平滑过渡

## 模块间协作关系

### 数据流向

```mermaid
graph TD
    A[原始数据] --> B[KsgGraph]
    B --> C[loadData]
    C --> D[renderData]
    D --> E[KsgPoints]
    D --> F[KsgLine]
    
    G[用户交互] --> H[KsgControls]
    G --> I[enterFocus]
    G --> J[enterGlobalView]
    
    K[视觉反馈] --> L[KsgLabel]
    K --> M[KsgHover]
    K --> N[focusCrust]
    
    E --> K
    F --> K
    H --> B
    I --> D
    J --> D
```

### 事件驱动架构

```typescript
// 典型的事件流
用户点击 → KsgControls检测 → enterFocus处理 → 
KsgGraph查询 → renderData更新 → KsgPoints/KsgLine渲染 → 
KsgLabel/focusCrust显示
```

## 性能优化策略

### 1. 计算优化
- **异步分片计算**：FrameScheduler避免阻塞
- **增量更新**：只计算变化的数据
- **空间索引**：快速查找和碰撞检测

### 2. 渲染优化
- **GPU加速**：自定义着色器并行处理
- **批处理**：减少Draw Call
- **内存管理**：对象池和资源复用

### 3. 交互优化
- **事件防抖**：避免频繁触发
- **状态缓存**：减少重复计算
- **渐进加载**：按需加载数据

## 扩展性设计

### 1. 模块化架构
每个模块职责单一，接口清晰，便于独立开发和测试。

### 2. 配置化系统
通过配置对象控制各模块行为，支持个性化定制。

### 3. 插件机制
支持自定义渲染器、控制器和特效插件。

### 4. 事件系统
基于EventDispatcher的松耦合通信机制。

## 最佳实践建议

### 1. 开发顺序
1. 理解数据处理层（KsgGraph → loadData → renderData）
2. 掌握渲染层（KsgPoints → KsgLine → 特效）
3. 学习交互层（KsgControls → 模式切换）

### 2. 调试技巧
- 使用浏览器的WebGL性能工具
- 监控FrameScheduler的任务队列
- 检查GPU内存使用情况

### 3. 性能调优
- 合理设置节点数量上限
- 调整动画帧率和持续时间
- 优化着色器代码复杂度

### 4. 内存管理
- 及时调用dispose()方法
- 避免创建不必要的临时对象
- 监控WebGL上下文状态

## 总结

KsgMap的核心模块设计体现了现代前端3D可视化的最佳实践：

- **分层架构**：数据、渲染、交互各层职责清晰
- **性能优先**：GPU加速、异步计算、内存优化
- **用户体验**：流畅动画、智能交互、响应式设计
- **可维护性**：模块化、配置化、注释完善

这种设计确保了系统在处理复杂知识图谱时的稳定性和可扩展性。
