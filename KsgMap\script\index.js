import path from "path";
import fs from "fs";
import { fileURLToPath } from "url";
const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);
const targetDir = path.resolve(
  __dirname,
  "../package/package.json"
);

getCrtVersion().then((packageJson) => {
  const updatedVersionStr = updateVersion(packageJson.version);
  packageJson.version = updatedVersionStr;
  // 写入目标目录中
  writeCrtVersion(packageJson, targetDir);
  writeCrtVersion(packageJson);
  console.log(`版本更新成功！更新后版本为${packageJson.version}`);
});

//读取数据
function getCrtVersion() {
  return new Promise((resolve, reject) => {
    fs.readFile(
      path.resolve(__dirname, "./template.json"),
      "utf-8",
      (err, data) => {
        if (!err) {
          resolve(JSON.parse(data));
        }
        reject(err);
      }
    );
  });
}

//写数据
function writeCrtVersion(data, p = path.resolve(__dirname, "./template.json")) {
  const pj = JSON.stringify(data);
  const str = pj.split(',').join("," + "\n")
  return new Promise((resolve, reject) => {
    fs.writeFile(p, str, (err) => {
      if (!err) {
        resolve();
      }
      reject();
    });
  });
}

//更新版本
function updateVersion(versionStr) {
  let versionArr = versionStr.split(".");
  versionArr = versionArr.map((item) => parseInt(item));
  for (let i = versionArr.length - 1; i >= 0; i--) {
    if (versionArr[i] <= 15) {
      versionArr[i]++;
      break;
    } else {
      continue;
    }
  }
  return versionArr.join(".");
}
