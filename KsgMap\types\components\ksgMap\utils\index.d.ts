import { Object3D, PerspectiveCamera, Group, Color } from "three";
import { POINT_STUDY_STATUS } from "../enums";
import KsgGraph from "../core/KsgGraph";
/**
 * @param value 添加单位
 * @returns
 */
export declare function addUnit(value?: string | number): string;
/**
 * 根据小球的学习状态获取颜色值并转换为RGB数组
 */
export declare function pointStatusToColor(pointStatus: POINT_STUDY_STATUS): [number, number, number];
/**控制器相关辅助函数 */
export declare function getClickArea(event: MouseEvent, dom: HTMLCanvasElement, subareas: Object3D, camera: PerspectiveCamera): any;
export declare function map(v: number, i1: number, i2: number, o1: number, o2: number): number;
export declare function constrain(v: number, min: number, max: number): number;
/**
 * 将一个数值从一个范围映射到另一个范围。
 *
 * 这个函数使用线性插值将输入值 `val` 从 `from` 数组定义的范围映射到 `to` 数组定义的范围。`from` 和 `to` 数组必须具有相同的长度，并且至少包含两个元素。`from` 必须单增
 */
export declare function cmap(val: number, from: number[], to: number[]): number;
export declare function equiv(a: number, b: number): boolean;
export declare function setNestedProperty(obj: any, path: string, value: any): void;
/**
 *生成的event函数
 */
export declare function createHoverPointEventFun(el: HTMLElement, camera: PerspectiveCamera, viewGroup: Group, enterCallback: (data: any) => void, leaveCallback: (data: any) => void): {
    clear: () => void;
    event: (e: MouseEvent) => void;
};
/**
 * 获取图中最大的y值
 * @param {KsgGraph} graph 图数据结构
 * @param {number} levelSpace 层级间距
 * @returns {number} 最大的y值
 */
export declare function getMaxY(graph: KsgGraph, levelSpace: number): number;
/**
 * 获取该坐标下的dom元素
 * @param {MouseEvent} e 事件对象
 * @returns {HTMLElement} dom元素
 */
export declare function getDomElement(e: MouseEvent): HTMLElement;
/**
 *
 * @param {Node} ele DOM元素
 * @returns 绑定的节点id
 */
export declare function findValidateParentNode(ele: Node | null): string | null;
/**
 * 计算未挂载标题label标签的宽高
 * @param {Element} el 需要进行计算的节点
 * @returns {{width:number,hight:number}} [宽,高]
 */
export declare function computedWH(el: Element): {
    width: number;
    height: number;
};
/**
 *计算场景中的模型在屏幕视口中的像素坐标位置
 * @param {Object3D} object 需要计算的对象
 * @param {number} rendererW 渲染器宽
 * @param {number} rendererH 渲染器高
 * @returns {{x:number,y:number}} x,y坐标(相对视口位置)
 */
export declare function getObjectPosition(object: Object3D, camera: PerspectiveCamera, rendererW: number, rendererH: number): {
    x: number;
    y: number;
};
/**
 * 知识点掌握状态所对应的颜色
 */
export declare function studyStatusToColor(studyStatus: POINT_STUDY_STATUS): Color;
