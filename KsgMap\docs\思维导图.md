## 导图

###  知识图谱项目整体架构图
```mermaid
graph LR
    %% 用户界面层
    subgraph "用户界面层 (UI Layer)"
        A[KsgMap.vue 主组件]
        B[控制按钮组件]
        C[加载状态显示]
    end
    
    %% 配置层
    subgraph "配置层 (Config Layer)"
        D[config/index.ts 配置管理]
        E[config/camera.ts 相机配置]
        F[config/renderer.ts 渲染器配置]
        G[config/scene.ts 场景配置]
        H[config/controls.ts 控制器配置]
        I[config/css2dRenderer.ts CSS2D渲染器]
        J[config/event.ts 事件系统]
    end
    
    %% 核心渲染层
    subgraph "核心渲染层 (Core Rendering)"
        K[core/KsgGraph.ts 图布局计算]
        L[core/KsgPoints.ts 节点渲染]
        M[core/KsgLine.ts 连线渲染]
        N[core/KsgControls.ts 相机控制]
        O[core/KsgHover.ts 悬停效果]
        P[core/KsgLabel.ts 标签渲染]
    end
    
    %% 着色器层
    subgraph "着色器层 (Shader Layer)"
        Q[shader/pointVert.glsl 节点顶点着色器]
        R[shader/pointFrag.glsl 节点片段着色器]
        S[shader/lineVert.glsl 连线顶点着色器]
        T[shader/lineFrag.glsl 连线片段着色器]
        U[shader/haloVert.glsl 光晕顶点着色器]
        V[shader/haloFrag.glsl 光晕片段着色器]
    end
    
    %% 动画系统
    subgraph "动画系统 (Animation System)"
        W[animation/KsgAnimation.ts 动画管理器]
        X[animation/point.ts 节点动画]
        Y[animation/enterFocus.ts 聚焦动画]
        Z[animation/enterGlobal.ts 全局视角动画]
        AA[animation/load.ts 加载动画]
    end
    
    %% 工具层
    subgraph "工具层 (Utils Layer)"
        BB[utils/FrameScheduler.ts 帧调度器]
        CC[utils/clickPointEvent.ts 点击事件]
        DD[utils/hoverObjectEvent.ts 悬停事件]
        EE[utils/mathJax.ts 数学公式渲染]
    end
    
    %% 全局状态
    subgraph "全局状态 (Global State)"
        FF[ctx/index.ts 全局上下文]
        GG[enums/index.ts 枚举定义]
        HH[types/index.ts 类型定义]
    end
    
    %% Three.js 核心
    subgraph "Three.js 核心"
        II[Scene 场景]
        JJ[Camera 相机]
        KK[Renderer 渲染器]
        LL[Geometry 几何体]
        MM[Material 材质]
    end
    
    %% 连接关系
    A --> D
    A --> J
    D --> E
    D --> F
    D --> G
    D --> H
    D --> I
    
    J --> CC
    J --> DD
    
    K --> BB
    L --> Q
    L --> R
    M --> S
    M --> T
    O --> U
    O --> V
    
    W --> X
    W --> Y
    W --> Z
    W --> AA
    
    FF --> K
    FF --> L
    FF --> M
    FF --> N
    
    E --> JJ
    F --> KK
    G --> II
    L --> LL
    L --> MM
    M --> LL
    M --> MM
    
```
### 组件整体架构
```mermaid
graph TB
    subgraph "应用层 Application Layer"
        A[App.vue] --> B[KsgMap.vue]
        A --> C[API接口层]
    end
    
    subgraph "组件层 Component Layer"
        B --> D[配置管理 Config]
        B --> E[事件处理 Events]
        B --> F[生命周期管理]
    end
    
    subgraph "核心计算层 Core Computing Layer"
        G[KsgGraph 图计算引擎] --> H[数据加载 loadData]
        G --> I[空间布局算法]
        G --> J[增量更新算法]
    end
    
    subgraph "渲染层 Rendering Layer"
        K[KsgPoints 节点渲染] --> L[自定义着色器]
        M[KsgLine 连线渲染] --> N[流光效果]
        O[KsgLabel 标签渲染] --> P[CSS2D渲染器]
    end
    
    subgraph "Three.js基础层 Three.js Foundation"
        Q[Scene 场景] --> R[Camera 相机]
        Q --> S[Renderer 渲染器]
        Q --> T[Controls 控制器]
    end
    
    subgraph "工具层 Utility Layer"
        U[FrameScheduler 任务调度] --> V[异步计算]
        W[动画系统 Animation] --> X[GSAP动画库]
        Y[事件系统 Events] --> Z[交互处理]
    end
    
    B --> G
    H --> K
    H --> M
    H --> O
    K --> Q
    M --> Q
    O --> Q
    G --> U
    K --> W
    M --> W


```
### 系统架构图
```mermaid
graph LR
    A[Vue应用入口 App.vue] --> B[KsgMap组件]
    B --> C[配置系统 config/]
    B --> D[核心渲染 core/]
    B --> E[动画系统 animation/]
    B --> F[着色器 shader/]
    B --> G[工具函数 utils/]
    
    C --> C1[场景配置 scene.ts]
    C --> C2[相机配置 camera.ts]
    C --> C3[渲染器配置 renderer.ts]
    C --> C4[控制器配置 controls.ts]
    C --> C5[事件配置 event.ts]
    
    D --> D1[图形计算 KsgGraph.ts]
    D --> D2[节点渲染 KsgPoints.ts]
    D --> D3[连线渲染 KsgLine.ts]
    D --> D4[标签渲染 KsgLabel.ts]
    D --> D5[悬停效果 KsgHover.ts]
    D --> D6[数据加载 loadData.ts]
    
    E --> E1[节点动画 point.ts]
    E --> E2[连线动画 load.ts]
    E --> E3[聚焦动画 enterFocus.ts]
    E --> E4[全局视图 enterGlobal.ts]
    E --> E5[标签动画 label.ts]
    
    F --> F1[节点着色器 pointVert/Frag.glsl]
    F --> F2[连线着色器 lineVert/Frag.glsl]
    F --> F3[光晕着色器 haloVert/Frag.glsl]
    
    G --> G1[数学计算 mathJax.ts]
    G --> G2[事件处理 clickPointEvent.ts]
    G --> G3[帧调度器 FrameScheduler.ts]
    G --> G4[Web Worker worker.ts]

```

### KsgMap Three.js 技术实现架构图
```mermaid
graph LR
    %% Three.js 核心组件
    subgraph "Three.js 核心组件"
        A[Scene 场景容器]
        B[PerspectiveCamera 透视相机]
        C[WebGLRenderer WebGL渲染器]
        D[CSS2DRenderer CSS2D渲染器]
    end
    
    %% 几何体和材质
    subgraph "几何体系统"
        E[BufferGeometry 缓冲几何体]
        F[Points 点云对象]
        G[LineSegments 线段对象]
        H[PlaneGeometry 平面几何体]
        I[SphereGeometry 球体几何体]
    end
    
    %% 材质系统
    subgraph "材质系统"
        J[ShaderMaterial 着色器材质]
        K[PointsMaterial 点材质]
        L[LineBasicMaterial 线材质]
        M[MeshBasicMaterial 网格材质]
    end
    
    %% 着色器程序
    subgraph "GLSL 着色器"
        N[顶点着色器 Vertex Shader]
        O[片段着色器 Fragment Shader]
        P[Uniform 变量]
        Q[Attribute 属性]
        R[Varying 变量]
    end
    
    %% 纹理系统
    subgraph "纹理系统"
        S[TextureLoader 纹理加载器]
        T[Texture 纹理对象]
        U[环境贴图 Environment Map]
        V[Alpha贴图 Alpha Map]
    end
    
    %% 控制系统
    subgraph "交互控制"
        W[OrbitControls 轨道控制器]
        X[Raycaster 射线投射]
        Y[Vector3 三维向量]
        Z[Quaternion 四元数]
    end
    
    %% 动画系统
    subgraph "动画系统"
        AA[TWEEN.js 补间动画]
        BB[Clock 时钟对象]
        CC[AnimationMixer 动画混合器]
        DD[requestAnimationFrame 帧循环]
    end
    
    %% 数学工具
    subgraph "数学工具"
        EE[Math 数学函数]
        FF[Matrix4 4x4矩阵]
        GG[Euler 欧拉角]
        HH[Color 颜色对象]
    end
    
    %% 连接关系
    A --> E
    A --> F
    A --> G
    B --> C
    C --> D
    
    E --> J
    F --> K
    G --> L
    H --> M
    I --> M
    
    J --> N
    J --> O
    N --> P
    N --> Q
    O --> R
    
    S --> T
    T --> U
    T --> V
    
    W --> X
    W --> Y
    W --> Z
    
    AA --> BB
    BB --> CC
    CC --> DD
    
    Y --> EE
    Z --> FF
    FF --> GG
    GG --> HH
    
    %% 自定义实现
    subgraph "自定义扩展"
        II[KsgControls 自定义控制器]
        JJ[KsgPoints 节点渲染器]
        KK[KsgLine 连线渲染器]
        LL[KsgHover 悬停效果]
        MM[FrameScheduler 帧调度器]
    end
    
    W --> II
    F --> JJ
    G --> KK
    H --> LL
    DD --> MM
    

```

### KsgMap 组件依赖关系图
```mermaid
graph LR
    %% Vue 组件层
    A[KsgMap.vue 主组件] --> B[useInitThreeJsConfig 配置初始化]
    A --> C[useScene 场景创建]
    A --> D[useCamera 相机创建]
    A --> E[useRenderer 渲染器创建]
    A --> F[useCSS2DRender CSS2D渲染器]
    A --> G[useControls 控制器创建]
    A --> H[useInitEvents 事件初始化]
    A --> I[useRenderFrame 渲染循环]
    
    %% 配置系统
    B --> J[ctx 全局上下文]
    C --> J
    D --> J
    E --> J
    F --> J
    G --> J
    
    %% 核心渲染组件
    J --> K[KsgGraph 图布局计算]
    J --> L[KsgPoints 节点渲染]
    J --> M[KsgLine 连线渲染]
    J --> N[KsgControls 相机控制]
    J --> O[KsgHover 悬停效果]
    J --> P[KsgLabel 标签系统]
    
    %% 图布局系统
    K --> Q[FrameScheduler 帧调度器]
    K --> R[clonePoint 节点克隆工具]
    
    %% 渲染系统
    L --> S[pointVert.glsl 节点顶点着色器]
    L --> T[pointFrag.glsl 节点片段着色器]
    M --> U[lineVert.glsl 连线顶点着色器]
    M --> V[lineFrag.glsl 连线片段着色器]
    O --> W[haloVert.glsl 光晕顶点着色器]
    O --> X[haloFrag.glsl 光晕片段着色器]
    
    %% 事件系统
    H --> Y[clickPointEvent 点击事件]
    H --> Z[hoverObjectEvent 悬停事件]
    H --> AA[globalViewEvent 全局视角事件]
    H --> BB[viewValidate 视图验证]
    
    %% 动画系统
    I --> CC[TWEEN.js 补间动画]
    I --> DD[KsgAnimation 动画管理器]
    DD --> EE[point.ts 节点动画]
    DD --> FF[enterFocus.ts 聚焦动画]
    DD --> GG[enterGlobal.ts 全局动画]
    DD --> HH[load.ts 加载动画]
    DD --> II[loadMore.ts 加载更多动画]
    
    %% 工具系统
    Y --> JJ[mathJax 数学公式]
    Z --> JJ
    K --> KK[studyStatusToColor 状态颜色映射]
    L --> KK
    
    %% 数据流
    LL[PointData 原始数据] --> K
    K --> MM[Point 处理后数据]
    MM --> L
    MM --> M
    MM --> O
    MM --> P
    
    %% Three.js 依赖
    C --> NN[Three.Scene]
    D --> OO[Three.PerspectiveCamera]
    E --> PP[Three.WebGLRenderer]
    F --> QQ[Three.CSS2DRenderer]
    L --> RR[Three.Points]
    L --> SS[Three.BufferGeometry]
    L --> TT[Three.ShaderMaterial]
    M --> UU[Three.LineSegments]
    N --> VV[Three.EventDispatcher]
    
    %% 样式设置


```

### 系统初始化和运行时序图
```mermaid
sequenceDiagram
    participant User as 用户
    participant Vue as KsgMap.vue
    participant Config as 配置系统
    participant Three as Three.js
    participant Graph as KsgGraph
    participant Render as 渲染系统
    participant Event as 事件系统
    participant Anim as 动画系统
    
    %% 初始化阶段
    Note over User,Anim: 系统初始化阶段
    User->>Vue: 创建组件实例
    Vue->>Config: useInitThreeJsConfig()
    Config->>Config: 合并用户配置和默认配置
    Config-->>Vue: 返回配置对象
    
    Vue->>Three: 创建Scene、Camera、Renderer
    Three-->>Vue: 返回Three.js对象实例
    
    Vue->>Event: useInitEvents()
    Event->>Event: 绑定鼠标、键盘事件监听器
    Event-->>Vue: 返回事件处理函数
    
    Vue->>Render: useRenderFrame()
    Render->>Render: 启动渲染循环
    Render-->>Vue: 开始帧循环
    
    %% 数据加载阶段
    Note over User,Anim: 数据加载阶段
    User->>Vue: firstLoadPointsData(data)
    Vue->>Graph: new KsgGraph(data)
    Graph->>Graph: build() - 构建图结构
    Graph->>Graph: computeLevel() - BFS计算层级
    Graph->>Graph: computePointPosition() - 计算坐标
    Graph-->>Vue: 返回处理后的节点数据
    
    Vue->>Render: 创建KsgPoints和KsgLine
    Render->>Three: 创建BufferGeometry和ShaderMaterial
    Three-->>Render: 返回渲染对象
    Render->>Three: 添加到场景中
    
    %% 运行时交互
    Note over User,Anim: 运行时交互阶段
    loop 渲染循环
        Render->>Three: renderer.render(scene, camera)
        Render->>Anim: TWEEN.update()
        Render->>Event: controls.update()
    end
    
    %% 用户交互
    User->>Event: 鼠标悬停节点
    Event->>Event: 射线检测intersectObject()
    Event->>Render: 显示悬停效果
    Render->>Anim: 启动悬停动画
    
    User->>Event: 点击节点
    Event->>Graph: 获取节点信息
    Event->>Anim: enterFocus() 聚焦动画
    Anim->>Three: 更新相机位置和目标
    
    User->>Event: 双击空白区域
    Event->>Anim: enterGlobal() 全局视角动画
    Anim->>Three: 切换到全局视角
    
    %% 数据更新
    User->>Vue: loadMorePointsData(newData)
    Vue->>Graph: loadMore(newData)
    Graph->>Graph: 计算差异数据DiffData
    Graph-->>Vue: 返回新增和更新的节点
    Vue->>Render: 更新渲染对象
    Vue->>Anim: 执行位置更新动画
    
    %% 错误处理
    alt 加载失败
        Vue->>User: 显示错误状态
    else 加载成功
        Vue->>User: 显示完整图谱
    end

```
### 数据处理流程图（事件层）
```mermaid
flowchart TD
    %% 数据输入
    A[原始节点数据 PointData] --> B[KsgGraph.compute]
    
    %% 图结构构建
    B --> C[build: 构建图结构]
    C --> D[解析父子关系]
    D --> E[创建Point对象]
    E --> F[建立ID映射表]
    
    %% 层级计算
    F --> G[computeLevel: BFS层级计算]
    G --> H[找到根节点]
    H --> I[广度优先遍历]
    I --> J[分配层级编号]
    J --> K[构建层级映射表]
    
    %% 位置计算
    K --> L[computePointPosition: 坐标计算]
    L --> M[计算每层节点数量]
    M --> N[计算层间距离]
    N --> O[计算节点间距]
    O --> P[分配3D坐标]
    
    %% 渲染数据准备
    P --> Q[KsgPoints: 节点渲染准备]
    Q --> R[创建BufferGeometry]
    R --> S[设置顶点属性]
    S --> T[位置/颜色/大小/透明度]
    T --> U[应用自定义着色器]
    
    %% 连线渲染
    P --> V[KsgLine: 连线渲染准备]
    V --> W[计算连线路径]
    W --> X[生成线段顶点]
    X --> Y[设置流光属性]
    Y --> Z[应用连线着色器]
    
    %% 渲染循环
    U --> A1[渲染循环 useRendererFrame]
    Z --> A1
    A1 --> B1[更新动画状态]
    B1 --> C1[相机控制更新]
    C1 --> D1[WebGL渲染]
    D1 --> E1[CSS2D标签渲染]
    E1 --> F1[TWEEN动画更新]
    
    %% 交互处理
    G1[用户交互事件] --> H1[event.ts 事件处理]
    H1 --> I1{事件类型}
    I1 -->|鼠标悬停| J1[显示悬停效果]
    I1 -->|节点点击| K1[进入聚焦模式]
    I1 -->|双击| L1[切换全局视角]
    I1 -->|相机控制| M1[更新视角位置]
    
    %% 动画系统
    J1 --> N1[KsgHover 悬停动画]
    K1 --> O1[enterFocus 聚焦动画]
    L1 --> P1[enterGlobal 全局动画]
    
    %% 增量更新
    Q1[新数据加载] --> R1[KsgGraph.loadMore]
    R1 --> S1[计算差异数据 DiffData]
    S1 --> T1[更新现有节点位置]
    T1 --> U1[添加新节点]
    U1 --> V1[执行位置动画]
    
```
### 数据处理流程图（文件层）
```mermaid
sequenceDiagram
    participant App as App.vue
    participant KsgMap as KsgMap.vue
    participant LoadData as loadData.ts
    participant KsgGraph as KsgGraph.ts
    participant Scheduler as FrameScheduler
    participant Render as renderData.ts
    participant Points as KsgPoints.ts
    
    App->>KsgMap: firstLoadPointsData(dataList, total)
    KsgMap->>LoadData: loadPointsData(pointsData, totalPoints, rootId)
    LoadData->>KsgGraph: new KsgGraph(pointsData)
    
    Note over KsgGraph: 数据处理阶段
    KsgGraph->>KsgGraph: build() - 构建图结构
    KsgGraph->>KsgGraph: computeLevel() - 计算层级
    KsgGraph->>Scheduler: addTask(computePointPosition)
    
    Note over Scheduler: 异步计算阶段
    Scheduler->>KsgGraph: 执行位置计算任务
    KsgGraph->>KsgGraph: computePointPosition() - 计算3D坐标
    
    Note over Scheduler: 计算完成回调
    Scheduler->>LoadData: onCompleted()
    LoadData->>Render: firstRenderSignalRootPoints()
    
    Note over Render: 渲染阶段
    Render->>Points: new KsgPoint(points, total, opacity, size)
    Points->>Points: 创建BufferGeometry和着色器材质
    Render->>App: 渲染完成，显示3D场景
```
### 数据流和渲染流程图
```mermaid
sequenceDiagram
    participant User as 用户操作
    participant App as App.vue
    participant KsgMap as KsgMap组件
    participant Config as 配置系统
    participant Graph as KsgGraph
    participant Points as KsgPoints
    participant Lines as KsgLine
    participant Shader as 着色器系统
    participant Animation as 动画系统
    participant Renderer as 渲染循环
    
    User->>App: 启动应用
    App->>KsgMap: 传入配置和API数据
    KsgMap->>Config: 初始化Three.js配置
    Config-->>KsgMap: 返回场景、相机、渲染器等
    
    KsgMap->>Graph: 传入原始节点数据
    Graph->>Graph: 执行DAG算法计算层级
    Graph->>Graph: 计算3D空间坐标
    Graph-->>KsgMap: 返回处理后的节点数据
    
    KsgMap->>Points: 创建节点几何体
    Points->>Shader: 应用节点着色器
    KsgMap->>Lines: 创建连线几何体  
    Lines->>Shader: 应用连线着色器
    
    KsgMap->>Animation: 触发进入动画
    Animation->>Points: 更新节点透明度/大小
    Animation->>Lines: 更新连线动画状态
    
    KsgMap->>Renderer: 启动渲染循环
    
    loop 每帧渲染
        Renderer->>Points: 更新节点状态
        Renderer->>Lines: 更新流光动画
        Renderer->>Shader: 执行着色器计算
        Shader-->>Renderer: 返回渲染结果
        Renderer->>User: 显示画面
    end
    
    User->>KsgMap: 点击节点
    KsgMap->>Animation: 触发聚焦动画
    Animation->>Points: 更新节点状态
    Animation->>Lines: 创建新连线
    KsgMap->>App: 触发loadMore事件
    App->>KsgMap: 返回新数据
    KsgMap->>Graph: 增量更新图数据

```
### Three.js渲染系统架构
```mermaid

graph LR
    subgraph "场景配置 Scene Setup"
        A[Scene 场景] --> B[背景纹理]
        A --> C[环境光照]
        A --> D[Group 容器组]
    end
    
    subgraph "相机系统 Camera System"
        E[PerspectiveCamera] --> F[视角控制]
        E --> G[OrbitControls]
        G --> H[缩放限制]
        G --> I[旋转限制]
    end
    
    subgraph "渲染器 Renderers"
        J[WebGLRenderer] --> K[抗锯齿]
        J --> L[透明度支持]
        M[CSS2DRenderer] --> N[HTML标签渲染]
    end
    
    subgraph "几何体与材质 Geometry & Materials"
        O[BufferGeometry] --> P[顶点位置]
        O --> Q[顶点颜色]
        O --> R[顶点大小]
        S[ShaderMaterial] --> T[自定义顶点着色器]
        S --> U[自定义片段着色器]
        S --> V[纹理贴图]
    end
    
    subgraph "渲染对象 Render Objects"
        W[KsgPoints 节点] --> O
        W --> S
        X[KsgLine 连线] --> Y[LineGeometry]
        X --> Z[流光着色器]
        AA[KsgLabel 标签] --> M
    end
    
    D --> W
    D --> X
    D --> AA
    E --> J
    J --> BB[渲染循环]
    M --> BB
```
### 要点思维导图
```mermaid
mindmap
  root((Three.js知识图谱系统))
    基础技术栈
      Vue 3
        Composition API
        TypeScript支持
        响应式数据
      Three.js核心
        Scene场景
        Camera相机
        Renderer渲染器
        Geometry几何体
        Material材质
      WebGL底层
        顶点着色器
        片段着色器
        缓冲区对象
        纹理贴图
    
    核心算法
      图算法
        DAG有向无环图
        拓扑排序
        BFS广度优先
        层级计算
      布局算法
        圆形分层布局
        力导向布局
        坐标变换
        空间映射
      动画算法
        Tween补间动画
        缓动函数
        关键帧动画
        物理模拟
    
    渲染技术
      几何体管理
        BufferGeometry
        BufferAttribute
        实例化渲染
        批量更新
      着色器编程
        GLSL语法
        Uniform变量
        Attribute属性
        Varying传递
      材质系统
        ShaderMaterial
        纹理采样
        透明度混合
        多通道渲染
    
    交互系统
      事件处理
        鼠标事件
        键盘事件
        触摸事件
        手势识别
      射线检测
        Raycaster
        碰撞检测
        对象选择
        距离计算
      相机控制
        OrbitControls
        自定义控制器
        动画相机
        视角切换
    
    性能优化
      渲染优化
        视锥体剔除
        LOD层次细节
        批处理技术
        GPU实例化
      内存管理
        对象池
        资源释放
        垃圾回收
        缓存策略
      计算优化
        Web Worker
        帧调度器
        分帧处理
        异步计算

```
### 学习时间规划甘特图
```mermaid
gantt
    title Three.js知识图谱系统学习计划
    dateFormat  YYYY-MM-DD
    section 第一阶段：基础知识
    Three.js基础概念        :done, basic1, 2024-01-01, 7d
    场景相机渲染器          :done, basic2, 2024-01-08, 7d
    几何体和材质           :active, basic3, 2024-01-15, 7d
    光照和阴影            :basic4, 2024-01-22, 7d
    
    section 第二阶段：进阶技术
    WebGL和着色器基础      :shader1, 2024-01-29, 10d
    GLSL语法学习          :shader2, 2024-02-08, 7d
    自定义着色器实现       :shader3, 2024-02-15, 10d
    动画系统学习          :anim1, 2024-02-25, 7d
    
    section 第三阶段：核心算法
    图算法理论学习         :algo1, 2024-03-04, 7d
    DAG算法实现           :algo2, 2024-03-11, 10d
    布局算法实现          :algo3, 2024-03-21, 10d
    空间坐标计算          :algo4, 2024-03-31, 7d
    
    section 第四阶段：系统集成
    组件架构设计          :sys1, 2024-04-07, 7d
    渲染系统集成          :sys2, 2024-04-14, 10d
    交互系统实现          :sys3, 2024-04-24, 10d
    动画系统集成          :sys4, 2024-05-04, 7d
    
    section 第五阶段：优化完善
    性能优化实现          :opt1, 2024-05-11, 10d
    内存管理优化          :opt2, 2024-05-21, 7d
    用户体验优化          :opt3, 2024-05-28, 7d
    项目总结和文档        :doc1, 2024-06-04, 7d

```
