<template>
  <div
    ref="container"
    :style="{
      height: addUnit(props.height),
      width: addUnit(props.width),
    }"
    class="ksg-three-container"
  >
    <div class="btn-container">
      <span class="back" @click="isFullScreen = !isFullScreen">
        <img width="20px" height="20px" v-show="!isFullScreen" title="全屏" src="./assets/images/full-icon.svg" alt="">
        <img width="20px" height="20px" v-show="isFullScreen" title="退出全屏" src="./assets/images/exitFull-icon.svg" alt=""></img>
      </span>
      <span class="back" @click="focusBack()">
        <img width="20px" height="20px" title="回退" src="./assets/images/back.svg" />
      </span>
      <span
        class="back"
        @click="focusBackToRoot"
        v-if="props.config.model === MODE.Single_ROOT"
      >
        <img width="20px" height="20px" title="回到根节点" src="./assets/images/top.svg" />
      </span>
    </div>
    <div class="loading" v-if="loadTip">
      <span class="loading-icon" v-if="props.loading === 'loading'">
        <img width="20px" height="20px" src="./assets/images/loading.svg"
      /></span>
      <span v-if="props.loading === 'error'">
        <img width="20px" height="20px" src="./assets/images/error.svg"
      /></span>
      <span> {{ loadTip }}</span>
    </div>
  </div>
</template>

<script lang="ts" setup>
/**
 * KsgMap 主组件
 * 
 * 这是 KsgMap 知识图谱可视化组件的主入口文件。
 * 该组件基于 Three.js 构建，提供了3D知识图谱的展示和交互功能。
 * 
 * 主要功能：
 * - 3D 场景的初始化和渲染
 * - 知识节点的加载和显示
 * - 用户交互事件处理（点击、悬停、缩放等）
 * - 全屏切换和导航控制
 * - 加载状态管理
 * 

 */

import {
  defineProps,
  defineEmits,
  defineExpose,
  type PropType,
  onMounted,
  onBeforeUnmount,
  ref,
  watch,
  nextTick,
} from "vue";
import { addUnit } from "./utils";
import useScene from "./config/scene";
import useCamera from "./config/camera";
import useRenderer from "./config/renderer";
import useCSS2DRender from "./config/css2dRenderer";
import useInitEvents from "./config/event";
import useRenderFrame from "./hooks/useRendererFrame";
import ctx from "./ctx";
import useControls from "./config/controls";
import { loadPointsData, loadMorePointsData } from "./core/loadData";
import { injectMathJax } from "./utils/mathJax";
import updateCanvasSize from "./config/update";
import { MODE, LOAD_STATUS } from "./enums";
import {useInitThreeJsConfig ,type Options } from "./config/index";
import type { PointData } from "./types";

/**
 * 组件属性定义
 */
const props = defineProps({
  /** 组件宽度，支持数字或字符串格式 */
  width: {
    type: [Number, String],
    default: 1200,
    required: false,
  },
  /** 组件高度，支持数字或字符串格式 */
  height: {
    type: [Number, String],
    default: 675,
    required: false,
  },
  /** Three.js 相关配置选项 */
  config: {
    type: Object as PropType<Options>,
    default: () => {
      return {};
    },
    required: false,
  },
  /** 加载状态：loading-加载中, loaded-已加载, error-错误 */
  loading: {
    type: String as PropType<"loading" | "loaded" | "error">,
    default: "loading",
  },
});

/**
 * 组件事件定义
 */
const emit = defineEmits<{
  /** 加载更多数据事件 */
  (e: "loadMore", rootId: string, current: number, levelSize: number): void;
  /** 点击标签事件 */
  (e: "clickLabel", id: string): void;
}>();

/** 容器DOM引用 */
const container = ref<HTMLElement>();
/** 全屏状态 */
const isFullScreen = ref(false);

/**
 * 初始化 Three.js 配置
 * 合并用户配置和默认配置，并根据组件尺寸动态计算相机宽高比
 */
const {
  cameraConfig,
  renderConfig,
  sceneConfig,
  controlsConfig,
  wrapperEleSizeConfig,
} = useInitThreeJsConfig({
  ...props.config,
  camera: {
    // 根据组件尺寸动态计算相机宽高比
    aspect: (props.width as number) / (props.height as number),
    ...props.config.camera,
  },
});

/**
 * 初始化 Three.js 核心模块
 */
/** WebGL 渲染器和 DOM 元素 */
const { rendererDom } = useRenderer(renderConfig!);
/** 相机实例 */
const { camera } = useCamera(cameraConfig!);
/** CSS2D 渲染器和 DOM 元素（用于 2D 标签渲染） */
const { css2dRendererDom } = useCSS2DRender(renderConfig!);
/** 3D 场景实例 */
const { scene } = useScene(sceneConfig!);
/** 轨道控制器实例 */
const { controls } = useControls(controlsConfig!);

/**
 * 初始化事件系统
 * 包括点击、悬停、加载更多等交互事件
 */
const {
  initEvents,
  destroyEvents,
  focusBackToRoot,
  focusBack,
  updateClickEventSize,
  updateHoverEventSize,
  changeLoadStatus,
} = useInitEvents(wrapperEleSizeConfig, {
  // 加载更多数据的回调函数
  loadMoreCallback: (rootId, current, levelSize) =>
    emit("loadMore", rootId, current, levelSize),
  // 点击标签的回调函数
  clickLabelCallback: (id) => emit("clickLabel", id),
});

/**
 * 初始化整个 3D 场景
 * 
 * 该函数负责完成以下初始化工作：
 * 1. 将渲染器 DOM 元素添加到容器中
 * 2. 初始化事件监听
 * 3. 启动渲染循环
 * 4. 设置容器尺寸监听器
 */
async function init() {
  // 将 WebGL 渲染器的 canvas 元素添加到容器
  container.value!.appendChild(rendererDom!);
  // 将 CSS2D 渲染器的元素添加到容器（用于渲染 2D 标签）
  container.value!.appendChild(css2dRendererDom!);
  // 初始化交互事件监听器
  initEvents(container.value!);
  
  // 获取渲染帧控制器并启动渲染循环
  const { startRenderFrame } = useRenderFrame();
  startRenderFrame();
  
  // 设置容器尺寸变化监听器
  const observe = new ResizeObserver(async () => {
    // 等待 DOM 更新完成
    await nextTick(() => {});
    // 更新画布尺寸和相关配置
    const { width, height } = updateCanvasSize(container.value!)!;
    // 更新事件系统的尺寸信息
    updateClickEventSize(width, height);
    updateHoverEventSize(width, height);
    
    // 如果当前尺寸与 props 尺寸一致且处于全屏状态，则退出全屏
    if (
      props.width === width &&
      props.height === height &&
      isFullScreen.value
    ) {
      isFullScreen.value = false;
    }
  });
  // 开始监听容器尺寸变化
  observe.observe(container.value!);
}

/**
 * 全屏状态监听器
 * 当全屏状态改变时，自动切换浏览器全屏模式
 */
watch(isFullScreen, (val: boolean) => {
  if (val) {
    // 进入全屏模式
    container.value?.requestFullscreen();
  } else {
    // 退出全屏模式
    document.exitFullscreen();
  }
});

/** 加载提示文本 */
const loadTip = ref("加载中...");

/**
 * 加载状态监听器
 * 根据 loading 属性的变化更新 UI 显示和内部状态
 */
watch(
  () => props.loading,
  (val: string) => {
    switch (val) {
      case "loading":
        loadTip.value = "加载中...";
        changeLoadStatus(LOAD_STATUS.loading);
        break;
      case "loaded":
        loadTip.value = "";
        changeLoadStatus(LOAD_STATUS.loaded);
        break;
      case "error":
        loadTip.value = "加载出错！请检查网络~";
        changeLoadStatus(LOAD_STATUS.error);
        break;
    }
  }
);

/**
 * 销毁场景和清理资源
 * 在组件卸载时调用，确保没有内存泄漏
 */
function destroy() {
  // 销毁事件监听器
  destroyEvents();
  // 清理场景对象
  ctx.scene?.remove(ctx.scene!);
}

// 组件生命周期钩子
/** 组件挂载时初始化场景 */
onMounted(init);
/** 组件卸载前清理资源 */
onBeforeUnmount(destroy);

/**
 * 暴露给父组件的方法
 * 
 * 这些方法允许父组件直接控制组件的核心功能
 */
defineExpose({
  /**
   * 首次加载知识点数据
   * @param pointsData - 知识点数据数组
   * @param total - 数据总数
   * @param rootId - 根节点ID（可选）
   * @returns Promise，完成加载后resolve
   */
  firstLoadPointsData:async (pointsData: PointData[],total:number,rootId?:string)=>{
    // 注入 MathJax 数学公式渲染支持
    // await injectMathJax(); 
    // 加载并渲染知识点数据
    return loadPointsData(pointsData,total,rootId);
  },
  
  /**
   * 加载更多知识点数据
   * 用于分页加载或动态扩展数据
   */
  loadMorePointsData,
  
  /**
   * 注入 MathJax 数学公式渲染器
   * 用于支持复杂的数学公式显示
   */
  // injectMathJax,
});
</script>

<style scoped>
.ksg-three-container {
  border-radius: 5px;
  overflow: hidden;
  position: relative;
  transition: all 0.25s;
}

.btn-container {
  position: absolute;
  right: 0px;
  bottom: 0px;
  background: rgba(0, 0, 0, 0.644);
  height: 35px;
  display: flex;
  align-items: center;
  border-top-left-radius: 10px;
}
.btn-container img{
  color: aqua;
}
.loading {
  position: absolute;
  background: rgba(0, 0, 0, 0.363);
  left: 0px;
  bottom: 0px;
  height: 35px;
  padding: 0px 10px;
  display: flex;
  align-items: center;
  gap: 8px;
  border-top-right-radius: 10px;
  color: white;
  user-select: none;
}
.loading img {
  vertical-align: middle;
}
.loading-icon {
  animation: loading 1s linear infinite;
}
@keyframes loading {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}
.back {
  color: rgba(255, 255, 255, 0.829);
  font-size: 20px;
  margin: 10px;
  cursor: pointer;
  z-index: 9999;
  user-select: none;
}
.back:hover {
  color: rgba(255, 255, 255, 1);
}
.back:active {
  color: rgba(255, 255, 255, 0.349);
}
@font-face {
  font-family: "alibaba-PuHuiTi";
  src: url(./assets/font/font.woff);
}
</style>
