import ctx from "../ctx";
import type { FocusData, Point } from "../types";
import { renderFocusData } from "../core/renderData";
import { ENTER_FOCUS_MODE } from "../enums";

/**
 * 聚焦模式处理模块 - enterFocus.ts
 *
 * 职责：
 * 1. 处理用户点击节点进入聚焦状态的逻辑
 * 2. 管理聚焦历史栈，支持前进后退操作
 * 3. 构建聚焦数据结构，协调视图切换
 * 4. 控制聚焦动画和子图渲染
 *
 * 核心概念：
 * - 聚焦状态：以某个节点为中心，显示其直接子节点的视图模式
 * - 历史栈：记录用户的浏览路径，支持回退操作
 * - 子图：聚焦节点及其直接子节点组成的局部图结构
 */

/**
 * 处理进入聚焦模式的核心函数
 *
 * 执行流程：
 * 1. 验证点击节点的有效性（避免重复聚焦）
 * 2. 从图数据中获取真实的节点信息
 * 3. 构建聚焦数据结构（聚焦节点 + 子节点集合）
 * 4. 更新历史栈（支持前进后退）
 * 5. 触发聚焦渲染动画
 *
 * @param point 被点击的知识节点对象
 * @param mode 进入模式 - ENTER：正常进入，BACK：历史回退
 * @returns Promise<void> 异步操作完成的承诺
 */
export function handleEnterFocus(
  point: Point,
  mode: ENTER_FOCUS_MODE = ENTER_FOCUS_MODE.ENTER
): Promise<void> {
  const { id } = point;

  // 步骤1：防止重复聚焦同一节点
  // 检查当前栈顶是否已经是要聚焦的节点
  if (
    mode === ENTER_FOCUS_MODE.ENTER &&
    ctx.focusStack!.length &&
    id === ctx.focusStack![ctx.focusStack!.length - 1]
  )
    return Promise.resolve();

  // 步骤2：从图数据源获取真实的节点信息
  // 注意：所有数据都必须从graph中获取，确保数据一致性
  const realPoint = ctx.graph!.getPointById(id)!;

  // 步骤3：构建聚焦数据结构
  // 包含聚焦节点本身和其所有直接子节点
  const focusData: FocusData = {
    focusPoint: realPoint, // 当前聚焦的节点
    rootPointId: id, // 聚焦节点的ID
    points: realPoint.childIds.map((id) => ctx.graph!.getPointById(id)!), // 直接子节点集合
  };

  // 步骤4：历史栈管理策略
  // 支持有限长度的历史记录，避免内存无限增长
  if (mode !== ENTER_FOCUS_MODE.BACK) {
    if (ctx.focusStack!.length >= ctx.focusStackMaxSize! + 1) {
      // 栈满时，保留根节点，移除最旧的记录，添加新记录
      const [rootId, ...others] = ctx.focusStack!;
      others.unshift(); // 移除最旧的记录
      others.push(id); // 添加新记录
      ctx.focusStack = [rootId, ...others];
    } else {
      // 栈未满时，直接添加到栈顶
      ctx.focusStack!.push(id);
    }
  }

  // 步骤5：执行聚焦渲染
  // 包含相机移动、节点高亮、连线动画等复合操作
  return renderFocusData(focusData);
}
