import { LOAD_STATUS, MODE } from "./enums/index";
import type { Options } from "./config";
import { App, Component } from "vue";
import ksgMap from "./KsgMap.vue";
import dataLoader from "./hooks/dataLoader";
import "./assets/style/css2dLabel.css";
declare const KsgMap: Component<typeof ksgMap>;
export { KsgMap, dataLoader };
export { LOAD_STATUS, MODE, Options };
declare const KsgMapGlobal: {
    install(app: App): void;
};
export default KsgMapGlobal;
