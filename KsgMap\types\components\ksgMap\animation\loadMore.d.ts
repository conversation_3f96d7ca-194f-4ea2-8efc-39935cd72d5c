import type { Point } from "../types";
import KsgPoint from "../core/KsgPoints";
import KsgLine from "../core/KsgLine";
import { KsgLabel } from "../core/KsgLabel";
import { FocusCrust } from "../core/focusCrust";
/** 发生变化的节点*/
type ModifyPoint = {
    old: Point;
    /**更新后 */
    new?: Point;
};
/**
 * @param {KsgPoint} pointsMesh 节点对象
 * @param {ModifyPoint} modifyPoints 发生变化的节点
 * @param {number} duration 动画持续时间
 */
export declare function updatePointPositionAnimation(pointsMesh: KsgPoint, line: KsgLine, focusCrust: FocusCrust, label: KsgLabel, modifyPoints: ModifyPoint, focusChildren: Point[], duration?: number): Promise<Point>;
export {};
