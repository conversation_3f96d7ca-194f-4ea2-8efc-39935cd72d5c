import { CSS2DRenderer } from "three/addons/renderers/CSS2DRenderer.js";
import ctx from "../ctx";
import type { RendererConfig } from "../types";

/**
 * 简化的配置类型 - 用于基本的CSS2D渲染器配置
 */
type Config = {
  width: number;        // 渲染器宽度
  height: number;       // 渲染器高度
  container: HTMLElement; // 容器DOM元素
};

/**
 * CSS2D渲染器配置函数 - 创建和配置Three.js的CSS2D渲染器
 *
 * CSS2DRenderer是Three.js的特殊渲染器，用于在3D场景中渲染HTML/CSS元素
 * 它可以将HTML元素（如文本标签、按钮等）精确地定位到3D空间中的特定位置
 *
 * 主要功能：
 * - 在3D场景中渲染HTML元素（标签、文本等）
 * - 自动处理HTML元素的3D位置转换为2D屏幕坐标
 * - 支持HTML元素的深度测试和遮挡关系
 * - 提供比WebGL文本渲染更丰富的样式和交互能力
 *
 * 应用场景：
 * - 知识节点的文本标签显示
 * - 3D场景中的UI界面元素
 * - 需要复杂样式的文本内容
 *
 * @param config 渲染器配置参数，支持简化配置或完整配置
 * @returns 返回包含CSS2D渲染器DOM元素的对象
 */
export default function useCSS2DRender(config: Config | RendererConfig) {
  // 通过配置对象的属性数量来判断配置类型
  // 简化配置通常只有3个属性：width, height, container
  // 完整配置会有更多属性
  if (Object.keys(config).length < 5) {
    // 处理简化配置的情况

    // 创建CSS2D渲染器实例
    const css2DRenderer = new CSS2DRenderer();

    // 设置渲染器尺寸 - 必须与WebGL渲染器尺寸一致
    // 这确保了HTML元素的定位计算正确
    css2DRenderer.setSize(config.width, config.height);

    // 设置DOM元素样式 - 绝对定位
    // position: absolute 使元素脱离文档流，可以精确定位
    css2DRenderer.domElement.style.position = "absolute";

    // 设置顶部偏移为0 - 与WebGL画布对齐
    css2DRenderer.domElement.style.top = "0px";

    // 如果提供了容器元素，将CSS2D渲染器添加到容器中
    if ((config as Config).container)
      (config as Config).container.appendChild(css2DRenderer.domElement);

    // 保存到全局上下文
    ctx.css2dRenderer = css2DRenderer;

  } else {
    // 处理完整配置的情况

    // 创建CSS2D渲染器实例
    const css2DRenderer = new CSS2DRenderer();

    // 设置渲染器尺寸
    css2DRenderer.setSize(config.width, config.height);

    // 设置DOM元素样式 - 绝对定位
    css2DRenderer.domElement.style.position = "absolute";
    css2DRenderer.domElement.style.top = "0px";

    // 将CSS2D渲染器添加到指定的DOM容器中
    // 这种方式允许更灵活的DOM结构组织
    (config as RendererConfig).css2DRenderer?.domElement.appendChild(
      css2DRenderer.domElement
    );

    // 保存到全局上下文
    ctx.css2dRenderer = css2DRenderer;
  }

  // 返回CSS2D渲染器的DOM元素
  // 这个DOM元素包含了所有的HTML标签和UI元素
  return {
    css2dRendererDom: ctx.css2dRenderer?.domElement,
  };
}
