/**
 * 创建鼠标移动事件处理器
 * 用于处理全局视图的鼠标移动事件，支持防抖功能
 * 当鼠标移动时立即触发移动事件，当鼠标停止移动一段时间后触发结束事件
 * 
 * @param onMove 移动时触发的事件回调函数，鼠标开始移动时立即调用
 * @param onMoveEnd 移动结束后并且在等待一段时间内没有进行任何操作后触发的事件回调函数
 * @param moveEndDelay 移动结束后等待时间（毫秒），默认100ms
 * @returns 包含事件处理函数和清理函数的对象
 */
export function createMouseMoveEvent(
  onMove: () => void,
  onMoveEnd: () => void,
  moveEndDelay: number = 100
) {
  /** 防抖定时器，用于延迟执行移动结束回调 */
  let timer: number | null | NodeJS.Timer = null;
  /**
   * 鼠标移动事件处理函数
   * 实现防抖逻辑：首次移动立即触发onMove，后续移动重置定时器
   * @param event 鼠标事件对象（当前未使用，但保留以便扩展）
   */
  function handleMoveEvent(event: MouseEvent) {
    // 如果没有定时器（首次移动或上次移动已结束），立即触发移动事件
    if (!timer) onMove();
    
    // 清除之前的定时器，重新开始计时
    clearTimeout(timer as number);
    
    // 设置新的定时器，在指定延迟后触发移动结束事件
    timer = setTimeout(() => {
      onMoveEnd();
      timer = null; // 重置定时器状态
    }, moveEndDelay);
  }

  /**
   * 清理函数
   * 用于清除定时器和重置状态，防止内存泄漏
   * 在组件卸载或不再需要事件处理器时调用
   */
  function clear() {
    clearTimeout(timer as number);
    timer = null;
  }
  
  // 返回公开的方法接口
  return {
    handleMoveEvent,  // 鼠标移动事件处理函数
    clear,           // 清理函数
  };
}
