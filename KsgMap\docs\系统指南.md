# Three.js知识图谱可视化系统学习指南
这个知识图谱系统是基于Vue 3 + Three.js构建的3D可视化知识图谱，核心目标是将抽象的知识节点关系以直观的3D空间形式展现，支持交互式探索和层次化加载。
## 项目架构

### 🔥 核心入口文件
- **`main.ts`** - Vue应用启动入口，创建Vue实例并挂载
- **`App.vue`** - 根组件，演示如何使用KsgMap组件，包含API调用和事件处理

### 🌐 网络层
- **`network/api.ts`** - 定义各种API接口，包括单根节点、多根节点、本地模式等数据获取方法

### 🎯 核心组件库 (`components/ksgMap/`)

#### 📋 主组件
- **`KsgMap.vue`** - 主要的Vue组件，负责整合所有子系统，处理生命周期和用户交互
- **`index.ts`** - 组件导出入口，提供全局注册和按需导入功能

#### ⚙️ 配置系统 (`config/`)
- **`index.ts`** - 配置系统入口，合并默认配置和用户配置
- **`scene.ts`** - Three.js场景配置（背景、雾效等）
- **`camera.ts`** - 相机配置（视角、投影参数）
- **`renderer.ts`** - 渲染器配置（WebGL渲染器、CSS2D渲染器）
- **`controls.ts`** - 控制器配置（鼠标交互、缩放限制）
- **`event.ts`** - 事件系统配置（点击、悬停、键盘事件）
- **`update.ts`** - 画布尺寸更新处理

#### 🎨 核心渲染系统 (`core/`)
- **`KsgGraph.ts`** - 🔑 **最核心** - DAG算法实现，负责图结构计算和空间布局
- **`KsgPoints.ts`** - 节点渲染类，管理所有节点的几何体和材质
- **`KsgLine.ts`** - 连线渲染类，实现流光效果和动态连线
- **`KsgLabel.ts`** - 标签渲染，使用CSS2D实现2D标签覆盖
- **`KsgHover.ts`** - 悬停效果管理，处理鼠标悬停时的视觉反馈
- **`KsgControls.ts`** - 自定义控制器，扩展OrbitControls功能
- **`loadData.ts`** - 数据加载和处理逻辑
- **`renderData.ts`** - 渲染数据管理和更新
- **`enterFocus.ts`** - 聚焦模式处理
- **`enterGlobalView.ts`** - 全局视图处理
- **`focusCrust.ts`** - 聚焦外壳效果

#### 🎬 动画系统 (`animation/`)
- **`KsgAnimation.ts`** - 动画管理器基类
- **`load.ts`** - 加载动画（节点进入、连线出现）
- **`loadMore.ts`** - 加载更多数据时的动画
- **`enterFocus.ts`** - 聚焦动画（相机移动、节点高亮）
- **`enterGlobal.ts`** - 全局视图动画
- **`point.ts`** - 节点相关动画
- **`line.ts`** - 连线相关动画
- **`label.ts`** - 标签动画
- **`mark.ts`** - 标记动画

#### 🎨 着色器系统 (`shader/`)
- **`pointVert.glsl`** / **`pointFrag.glsl`** - 节点着色器（支持呼吸动画、大小变化）
- **`lineVert.glsl`** / **`lineFrag.glsl`** - 连线着色器（流光效果）
- **`haloVert.glsl`** / **`haloFrag.glsl`** - 光晕效果着色器
- **`vert.glsl`** / **`frag.glsl`** - 通用着色器

#### 🔧 工具和辅助系统
- **`types/index.ts`** - TypeScript类型定义
- **`enums/index.ts`** - 枚举常量定义
- **`ctx/index.ts`** - 全局上下文管理，维护Three.js对象实例
- **`utils/`** - 工具函数集合
  - `FrameScheduler.ts` - 帧调度器，分帧处理计算任务
  - `clickPointEvent.ts` - 点击事件处理
  - `hoverObjectEvent.ts` - 悬停事件处理
  - `mathJax.ts` - 数学公式渲染
- **`hooks/`** - Vue组合式函数
  - `useRendererFrame.ts` - 渲染循环管理
  - `dataLoader.ts` - 数据加载钩子
- **`workers/`** - Web Worker
  - `worker.ts` - 主Worker文件
  - `calculateGraph.ts` - 图计算Worker

## 📖 推荐阅读顺序

### 🥇 第一阶段：理解整体架构 (1-2天)
1. **`main.ts`** - 了解应用启动流程
2. **`App.vue`** - 理解组件使用方式和数据流
3. **`components/ksgMap/index.ts`** - 了解组件导出结构
4. **`KsgMap.vue`** - 理解主组件的生命周期和职责

### 🥈 第二阶段：掌握配置系统 (2-3天)
5. **`config/index.ts`** - 理解配置合并逻辑
6. **`types/index.ts`** - 熟悉数据结构定义
7. **`enums/index.ts`** - 了解常量定义
8. **`ctx/index.ts`** - 理解全局状态管理
9. **`config/scene.ts`** → **`camera.ts`** → **`renderer.ts`** → **`controls.ts`** - 按顺序理解Three.js配置

### 🥉 第三阶段：核心算法实现 (3-4天)
10. **`core/KsgGraph.ts`** - 🔑 **重点** - DAG算法和空间布局
11. **`core/loadData.ts`** - 数据加载和预处理
12. **`core/renderData.ts`** - 渲染数据管理

### 🏅 第四阶段：渲染系统 (4-5天)
13. **`core/KsgPoints.ts`** - 节点渲染实现
14. **`shader/pointVert.glsl`** + **`pointFrag.glsl`** - 节点着色器
15. **`core/KsgLine.ts`** - 连线渲染实现
16. **`shader/lineVert.glsl`** + **`lineFrag.glsl`** - 连线着色器
17. **`core/KsgLabel.ts`** - 标签渲染
18. **`hooks/useRendererFrame.ts`** - 渲染循环

### 🎖️ 第五阶段：交互和动画 (3-4天)
19. **`config/event.ts`** - 事件系统配置
20. **`core/KsgHover.ts`** - 悬停效果
21. **`utils/clickPointEvent.ts`** - 点击事件处理
22. **`animation/load.ts`** - 基础动画
23. **`animation/enterFocus.ts`** - 聚焦动画
24. **`core/enterFocus.ts`** + **`enterGlobalView.ts`** - 视图切换

### 🏆 第六阶段：优化和扩展 (2-3天)
25. **`utils/FrameScheduler.ts`** - 性能优化
26. **`workers/calculateGraph.ts`** - Web Worker优化
27. **`animation/`** 其他动画文件 - 了解完整动画系统

## 🎯 关键理解要点

### 🔥 核心数据流
```
原始数据 → KsgGraph(DAG算法) → 空间坐标 → KsgPoints/KsgLine → 着色器渲染 → 屏幕显示
```

### 🎨 渲染管线
```
BufferGeometry → ShaderMaterial → Points/LineSegments → Scene → Renderer → Canvas
```

### 🎬 动画系统
```
Tween.js → 属性更新 → BufferAttribute.needsUpdate → 重新渲染
```

### ⚡ 性能优化
```
FrameScheduler → 分帧计算
Web Worker → 后台计算
BufferGeometry → 批量渲染
```


## 项目概述

这是一个基于Vue 3 + TypeScript + Three.js构建的知识图谱可视化系统，主要用于展示知识点之间的层级关系和依赖关系。系统采用DAG（有向无环图）算法进行节点布局，支持3D可视化、动画效果、用户交互等功能。

## 核心技术栈分析

### 1. 前端框架层
- **Vue 3**: 使用Composition API构建响应式UI
- **TypeScript**: 提供类型安全和更好的开发体验
- **Vite**: 现代化的构建工具

### 2. 3D渲染层
- **Three.js**: 核心3D渲染引擎
- **WebGL**: 底层图形API，通过着色器实现高性能渲染
- **CSS2DRenderer**: 用于渲染2D标签覆盖层

### 3. 数学计算层
- **DAG算法**: 有向无环图的拓扑排序和层级计算
- **空间几何**: 3D坐标系统和变换矩阵
- **动画插值**: Tween.js实现平滑动画过渡

## 系统架构深度解析

### 核心组件架构

#### 1. KsgMap.vue - 主组件
```typescript
// 主要职责：
- 初始化Three.js场景
- 管理组件生命周期
- 处理用户交互事件
- 协调各个子系统
```

#### 2. 配置系统 (config/)
- **scene.ts**: 场景配置（背景、光照、雾效）
- **camera.ts**: 相机配置（视角、投影参数）
- **renderer.ts**: 渲染器配置（抗锯齿、尺寸）
- **controls.ts**: 控制器配置（鼠标交互、缩放限制）

#### 3. 核心渲染系统 (core/)

**KsgGraph.ts - 图形计算核心**
```typescript
// 核心算法：
1. 构建图结构：将输入数据转换为图结构
2. 拓扑排序：计算节点层级关系
3. 空间布局：为每个节点分配3D坐标
4. 增量更新：支持动态加载更多数据
```

**KsgPoints.ts - 节点渲染**
```typescript
// 技术要点：
- BufferGeometry: 高性能几何体
- BufferAttribute: 顶点属性（位置、颜色、大小）
- 实例化渲染: 批量渲染大量节点
- 动态更新: 实时修改节点状态
```

**KsgLine.ts - 连线渲染**
```typescript
// 实现特色：
- 流光效果: 通过着色器实现动态流光
- 曲线连接: 贝塞尔曲线连接节点
- 批量渲染: LineSegments优化性能
```

### 着色器系统 (shader/)

#### 节点着色器
**pointVert.glsl (顶点着色器)**
```glsl
// 功能：
- 计算节点在屏幕上的位置
- 根据距离调整节点大小
- 传递颜色和透明度信息
```

**pointFrag.glsl (片段着色器)**
```glsl
// 功能：
- 纹理采样和混合
- 呼吸动画效果
- 透明度处理
```

#### 连线着色器
**lineVert.glsl / lineFrag.glsl**
```glsl
// 实现流光效果：
- 时间动画参数
- 渐变色彩计算
- 动态透明度
```

### 动画系统 (animation/)

#### 动画类型
1. **节点进入动画**: 淡入 + 缩放
2. **连线流光动画**: 沿路径移动的光效
3. **聚焦动画**: 相机平滑移动到目标节点
4. **呼吸动画**: 节点周期性透明度变化

#### 动画实现原理
```typescript
// 使用Tween.js实现：
- 缓动函数: 提供自然的动画曲线
- 链式动画: 支持动画序列
- 回调机制: 动画完成后的处理
```

## 关键技术点详解

### 1. DAG算法实现

**拓扑排序算法**
```typescript
// 核心步骤：
1. 计算每个节点的入度
2. 将入度为0的节点加入队列
3. 逐层处理节点，更新子节点入度
4. 分配层级和坐标
```

**空间布局算法**
```typescript
// 圆形分层布局：
- 单节点：放置在层级中心
- 多节点：按圆形分布，支持多圈嵌套
- 层级间距：Y轴方向等间距分布
```

### 2. 高性能渲染优化

**BufferGeometry优化**
```typescript
// 性能优化策略：
- 预分配顶点缓冲区
- 批量更新属性
- 避免频繁的几何体重建
- 使用索引缓冲区减少顶点数量
```

**视锥体剔除**
```typescript
// 渲染优化：
- frustumCulled: false // 禁用自动剔除
- 手动实现基于距离的LOD
- 动态调整渲染精度
```

### 3. 内存管理

**资源释放机制**
```typescript
// 防止内存泄漏：
- geometry.dispose() // 释放几何体
- material.dispose() // 释放材质
- texture.dispose()  // 释放纹理
- 移除事件监听器
```


## 核心代码解析

### 节点渲染核心代码
```typescript
// KsgPoints.ts 关键实现
class KsgPoint extends Points {
  constructor(points: Point[], total: number) {
    // 创建几何体
    const pGeo = new BufferGeometry();
    
    // 设置顶点属性
    const positionAttribute = new BufferAttribute(
      new Float32Array(total * 3), 3
    );
    const colorAttribute = new BufferAttribute(
      new Float32Array(total * 3), 3
    );
    
    // 应用自定义着色器材质
    const material = new ShaderMaterial({
      vertexShader: pointVertexShader,
      fragmentShader: pointFragmentShader,
      uniforms: {
        uTime: { value: 0 },
        map: { value: texture },
        // ... 其他uniform
      }
    });
  }
}
```

### 动画系统核心代码
```typescript
// 节点进入动画
function pointEnterAnimation(pointsMesh: KsgPoint, indexArr: number[]) {
  return new Promise((resolve) => {
    // 初始状态：透明 + 缩小
    pointsMesh.updateOpacity(indexArr, 0);
    pointsMesh.updateSize(indexArr, 0);
    
    // 动画到最终状态
    new TWEEN.Tween({ opacity: 0, size: 0 })
      .to({ opacity: 1, size: 10 }, 1000)
      .easing(TWEEN.Easing.Cubic.Out)
      .onUpdate((obj) => {
        pointsMesh.updateOpacity(indexArr, obj.opacity);
        pointsMesh.updateSize(indexArr, obj.size);
      })
      .onComplete(resolve)
      .start();
  });
}
```



## 详细技术实现解析

### 数据结构设计

#### 节点数据结构
```typescript
interface Point {
  id: string;                    // 唯一标识
  name: string;                  // 显示名称
  parentIds: string[];           // 父节点ID数组
  childIds: string[];            // 子节点ID数组
  level: number;                 // 层级（Y轴位置）
  coordinate: [number, number, number]; // 3D坐标
  isMilestone: boolean;          // 是否为里程碑节点
  status: number;                // 学习状态
  index?: number;                // 渲染索引
}
```

#### 图结构管理
```typescript
class KsgGraph {
  pointsData: Map<string, Point>;           // 节点数据映射
  idLevelMap: { [level: number]: string[] }; // 层级映射
  diffData: DiffData;                       // 增量数据

  // 核心算法：拓扑排序 + 空间布局
  compute(pointsData: PointData[]) {
    this.build(pointsData);           // 构建图结构
    this.computeLevel(this.pointsData); // 计算层级
    this.computePointPosition();      // 计算坐标
  }
}
```

### 渲染管线深度解析

#### 1. 几何体构建
```typescript
// KsgPoints.ts - 节点几何体构建
constructor(points: Point[], total: number) {
  const pGeo = new BufferGeometry();

  // 顶点位置属性 (x, y, z)
  const positionAttribute = new BufferAttribute(
    new Float32Array(total * 3), 3
  );

  // 顶点颜色属性 (r, g, b)
  const colorAttribute = new BufferAttribute(
    new Float32Array(total * 3), 3
  );

  // 透明度属性
  const opacityAttribute = new BufferAttribute(
    new Float32Array(total), 1
  );

  // 大小属性
  const sizeAttribute = new BufferAttribute(
    new Float32Array(total), 1
  );

  // 呼吸动画状态
  const breathAttribute = new BufferAttribute(
    new Float32Array(total), 1
  );

  // 随机相位（用于呼吸动画）
  const randomAttribute = new BufferAttribute(
    new Float32Array(total), 1
  );

  // 设置几何体属性
  pGeo.setAttribute('position', positionAttribute);
  pGeo.setAttribute('customColor', colorAttribute);
  pGeo.setAttribute('customOpacity', opacityAttribute);
  pGeo.setAttribute('size', sizeAttribute);
  pGeo.setAttribute('breathStatus', breathAttribute);
  pGeo.setAttribute('customRandom', randomAttribute);
}
```

#### 2. 着色器材质系统
```glsl
// pointVert.glsl - 顶点着色器详解
attribute float size;           // 节点大小
attribute vec3 customColor;     // 自定义颜色
attribute float customOpacity;  // 自定义透明度
attribute float customRandom;   // 随机值
attribute float breathStatus;   // 呼吸状态

varying vec3 vColor;           // 传递给片段着色器的颜色
varying float vOpacity;        // 传递给片段着色器的透明度
varying vec2 vUv;             // UV坐标
varying float vCustomRandom;   // 传递随机值
varying float vIsBreathAni;    // 传递呼吸状态

void main() {
    vUv = uv;
    vColor = customColor;
    vOpacity = customOpacity;
    vCustomRandom = customRandom;
    vIsBreathAni = breathStatus;

    // 计算模型视图位置
    vec4 mvPosition = modelViewMatrix * vec4(position, 1.0);

    // 根据距离调整点大小（透视效果）
    gl_PointSize = 10.0 * (350.0 / -mvPosition.z);

    // 计算最终位置
    gl_Position = projectionMatrix * mvPosition;
}
```

```glsl
// pointFrag.glsl - 片段着色器详解
varying vec3 vColor;
varying float vOpacity;
uniform sampler2D map;       // 颜色贴图
uniform sampler2D alphaMap;  // 透明度贴图
uniform vec2 offset;         // 纹理偏移量
uniform float uTime;         // 动画时间

varying vec2 vUv;
varying float vCustomRandom; // 呼吸时的随机相位
varying float vIsBreathAni;  // 是否开启呼吸动画

void main() {
    // 采样颜色贴图
    vec4 texColor = texture2D(map, gl_PointCoord + offset);
    // 采样透明度贴图
    float alpha = texture2D(alphaMap, gl_PointCoord + offset).a;

    // 呼吸动画处理
    if(vIsBreathAni == 1.0) {
        // 基于时间和随机相位的正弦波动画
        float finalOpacity = sin(uTime + vCustomRandom) * 0.4 + 0.6;
        vec4 finalColor = vec4(vColor * texColor.rgb, finalOpacity);
        gl_FragColor = finalColor;
    } else {
        // 正常渲染
        vec4 finalColor = vec4(vColor * texColor.rgb, texColor.a * alpha * vOpacity);
        gl_FragColor = finalColor;
    }
}
```

#### 3. 连线渲染系统
```typescript
// KsgLine.ts - 连线几何体构建
class KsgLine extends LineSegments {
  constructor(focusPoint: Point, focusChildren: Point[]) {
    const positions: number[] = [];
    const colors: number[] = [];
    const indices: number[] = [];
    const segmentProgress: number[] = [];

    // 为每条连线创建顶点
    focusChildren.forEach((child, index) => {
      // 起点（聚焦节点）
      positions.push(
        focusPoint.coordinate[0],
        focusPoint.coordinate[1],
        focusPoint.coordinate[2]
      );

      // 终点（子节点）
      positions.push(
        child.coordinate[0],
        child.coordinate[1],
        child.coordinate[2]
      );

      // 连线颜色
      colors.push(1, 1, 1, 1, 1, 1); // 白色

      // 线段索引
      indices.push(index * 2, index * 2 + 1);

      // 流光进度
      segmentProgress.push(0, 1);
    });

    const geometry = new BufferGeometry();
    geometry.setAttribute('position', new Float32BufferAttribute(positions, 3));
    geometry.setAttribute('color', new Float32BufferAttribute(colors, 3));
    geometry.setIndex(indices);

    // 自定义着色器材质
    const material = new ShaderMaterial({
      vertexShader: lineVertexShader,
      fragmentShader: lineFragmentShader,
      uniforms: {
        uTime: { value: 0 },
        uWidth: { value: 0.3 },    // 流光宽度
        uSpeed: { value: 0.015 },  // 流光速度
        uIsRandom: { value: true } // 是否随机
      },
      transparent: true
    });

    super(geometry, material);
  }
}
```

### 动画系统实现细节

#### 1. Tween动画框架
```typescript
// animation/load.ts - 节点进入动画
export function pointEnterAnimation(
  pointsMesh: KsgPoint,
  indexArr: number[],
  delay: number = 0
): Promise<void> {
  return new Promise((resolve) => {
    // 设置初始状态
    pointsMesh.updateOpacity(indexArr, 0);
    pointsMesh.updateSize(indexArr, 0);

    // 创建动画对象
    const tween = new TWEEN.Tween({ opacity: 0, size: 0 })
      .to({ opacity: 1, size: 10 }, 800) // 800ms动画时长
      .delay(delay) // 延迟执行
      .easing(TWEEN.Easing.Cubic.Out) // 缓动函数
      .onUpdate((values) => {
        // 更新节点属性
        pointsMesh.updateOpacity(indexArr, values.opacity);
        pointsMesh.updateSize(indexArr, values.size);
      })
      .onComplete(() => {
        resolve();
      });

    tween.start();
  });
}
```

#### 2. 相机动画系统
```typescript
// animation/enterFocus.ts - 聚焦动画
export function viewMoveAnimation(
  camera: PerspectiveCamera,
  controls: KsgControls,
  targetPosition: [number, number, number],
  targetLookAt: [number, number, number]
): Promise<void> {
  return new Promise((resolve) => {
    const startPosition = camera.position.clone();
    const startTarget = controls.target.clone();

    const endPosition = new Vector3(...targetPosition);
    const endTarget = new Vector3(...targetLookAt);

    new TWEEN.Tween({
      posX: startPosition.x,
      posY: startPosition.y,
      posZ: startPosition.z,
      targetX: startTarget.x,
      targetY: startTarget.y,
      targetZ: startTarget.z
    })
    .to({
      posX: endPosition.x,
      posY: endPosition.y,
      posZ: endPosition.z,
      targetX: endTarget.x,
      targetY: endTarget.y,
      targetZ: endTarget.z
    }, 1500)
    .easing(TWEEN.Easing.Cubic.InOut)
    .onUpdate((values) => {
      // 更新相机位置
      camera.position.set(values.posX, values.posY, values.posZ);
      // 更新控制器目标
      controls.target.set(values.targetX, values.targetY, values.targetZ);
      controls.update();
    })
    .onComplete(resolve)
    .start();
  });
}
```

### 交互系统实现

#### 1. 射线检测
```typescript
// utils/clickPointEvent.ts - 点击检测
export function setupClickDetection(
  container: HTMLElement,
  camera: PerspectiveCamera,
  pointsMesh: KsgPoint
) {
  const raycaster = new Raycaster();
  const mouse = new Vector2();

  // 设置射线检测参数
  raycaster.params.Points.threshold = 2; // 点击容差

  container.addEventListener('click', (event) => {
    // 计算标准化设备坐标
    const rect = container.getBoundingClientRect();
    mouse.x = ((event.clientX - rect.left) / rect.width) * 2 - 1;
    mouse.y = -((event.clientY - rect.top) / rect.height) * 2 + 1;

    // 设置射线
    raycaster.setFromCamera(mouse, camera);

    // 检测相交对象
    const intersects = raycaster.intersectObject(pointsMesh);

    if (intersects.length > 0) {
      const intersection = intersects[0];
      const pointIndex = intersection.index;

      // 获取点击的节点数据
      const pointData = pointsMesh.getPointData(pointIndex);
      if (pointData) {
        // 处理节点点击事件
        handleNodeClick(pointData);
      }
    }
  });
}
```

#### 2. 悬停效果
```typescript
// core/KsgHover.ts - 悬停效果管理
class KsgHover {
  static update(ctx: Context, deltaTime: number) {
    if (!ctx.pointsMesh || !ctx.camera) return;

    // 检测鼠标悬停
    const intersects = ctx.raycaster?.intersectObject(ctx.pointsMesh);

    if (intersects && intersects.length > 0) {
      const pointIndex = intersects[0].index;

      // 如果是新的悬停节点
      if (ctx.pointsMesh.lastHoverIndex !== pointIndex) {
        // 恢复上一个悬停节点
        if (ctx.pointsMesh.lastHoverIndex !== -1) {
          ctx.pointsMesh.updateSize([ctx.pointsMesh.lastHoverIndex], 10);
        }

        // 高亮当前悬停节点
        ctx.pointsMesh.updateSize([pointIndex], 15);
        ctx.pointsMesh.lastHoverIndex = pointIndex;

        // 显示悬停标签
        const pointData = ctx.pointsMesh.getPointData(pointIndex);
        if (pointData) {
          showHoverLabel(pointData, intersects[0].point);
        }
      }
    } else {
      // 没有悬停对象，恢复状态
      if (ctx.pointsMesh.lastHoverIndex !== -1) {
        ctx.pointsMesh.updateSize([ctx.pointsMesh.lastHoverIndex], 10);
        ctx.pointsMesh.lastHoverIndex = -1;
        hideHoverLabel();
      }
    }
  }
}
```

### 性能优化技术

#### 1. 帧调度器
```typescript
// utils/FrameScheduler.ts - 分帧处理
class FrameScheduler {
  private tasks: (() => boolean)[] = [];
  private isRunning = false;

  addTask(task: () => boolean) {
    this.tasks.push(task);
    if (!this.isRunning) {
      this.start();
    }
  }

  private start() {
    this.isRunning = true;
    this.processFrame();
  }

  private processFrame() {
    const startTime = performance.now();
    const maxFrameTime = 16; // 16ms预算

    while (this.tasks.length > 0 && (performance.now() - startTime) < maxFrameTime) {
      const task = this.tasks.shift()!;
      const shouldContinue = task();

      if (shouldContinue) {
        this.tasks.push(task); // 任务未完成，重新加入队列
      }
    }

    if (this.tasks.length > 0) {
      requestAnimationFrame(() => this.processFrame());
    } else {
      this.isRunning = false;
    }
  }
}
```

#### 2. Web Worker计算
```typescript
// workers/calculateGraph.ts - 图计算Worker
self.onmessage = function(e) {
  const { pointsData, config } = e.data;

  try {
    // 在Worker中执行计算密集任务
    const graph = new GraphCalculator(pointsData, config);
    const result = graph.calculate();

    // 返回计算结果
    self.postMessage({
      success: true,
      data: result
    });
  } catch (error) {
    self.postMessage({
      success: false,
      error: error.message
    });
  }
};

// 主线程使用Worker
const worker = new Worker('./workers/calculateGraph.ts');
worker.postMessage({ pointsData, config });
worker.onmessage = (e) => {
  const { success, data, error } = e.data;
  if (success) {
    // 使用计算结果更新渲染
    updateVisualization(data);
  } else {
    console.error('Worker计算错误:', error);
  }
};
```

## 实战练习项目

### 练习1：基础Three.js场景搭建 (第1-2周)

#### 目标
创建一个基本的Three.js场景，包含几何体、光照和相机控制。

#### 实现步骤
```typescript
// 1. 创建基础场景
import * as THREE from 'three';
import { OrbitControls } from 'three/examples/jsm/controls/OrbitControls';

class BasicScene {
  private scene: THREE.Scene;
  private camera: THREE.PerspectiveCamera;
  private renderer: THREE.WebGLRenderer;
  private controls: OrbitControls;

  constructor(container: HTMLElement) {
    this.initScene();
    this.initCamera();
    this.initRenderer(container);
    this.initControls();
    this.initLights();
    this.createObjects();
    this.animate();
  }

  private initScene() {
    this.scene = new THREE.Scene();
    this.scene.background = new THREE.Color(0x1a1a1a);
  }

  private initCamera() {
    this.camera = new THREE.PerspectiveCamera(
      75, // fov
      window.innerWidth / window.innerHeight, // aspect
      0.1, // near
      1000 // far
    );
    this.camera.position.set(5, 5, 5);
  }

  private initRenderer(container: HTMLElement) {
    this.renderer = new THREE.WebGLRenderer({ antialias: true });
    this.renderer.setSize(window.innerWidth, window.innerHeight);
    this.renderer.shadowMap.enabled = true;
    this.renderer.shadowMap.type = THREE.PCFSoftShadowMap;
    container.appendChild(this.renderer.domElement);
  }

  private initControls() {
    this.controls = new OrbitControls(this.camera, this.renderer.domElement);
    this.controls.enableDamping = true;
    this.controls.dampingFactor = 0.05;
  }

  private initLights() {
    // 环境光
    const ambientLight = new THREE.AmbientLight(0x404040, 0.6);
    this.scene.add(ambientLight);

    // 方向光
    const directionalLight = new THREE.DirectionalLight(0xffffff, 0.8);
    directionalLight.position.set(10, 10, 5);
    directionalLight.castShadow = true;
    this.scene.add(directionalLight);
  }

  private createObjects() {
    // 创建一些基础几何体
    const geometry = new THREE.BoxGeometry();
    const material = new THREE.MeshLambertMaterial({ color: 0x00ff00 });
    const cube = new THREE.Mesh(geometry, material);
    cube.castShadow = true;
    this.scene.add(cube);

    // 添加地面
    const planeGeometry = new THREE.PlaneGeometry(20, 20);
    const planeMaterial = new THREE.MeshLambertMaterial({ color: 0x888888 });
    const plane = new THREE.Mesh(planeGeometry, planeMaterial);
    plane.rotation.x = -Math.PI / 2;
    plane.position.y = -2;
    plane.receiveShadow = true;
    this.scene.add(plane);
  }

  private animate() {
    requestAnimationFrame(() => this.animate());
    this.controls.update();
    this.renderer.render(this.scene, this.camera);
  }
}
```

### 练习2：自定义着色器实现 (第3-4周)

#### 目标
创建自定义着色器实现特殊视觉效果。

#### 实现步骤
```glsl
// vertexShader.glsl - 波动效果顶点着色器
uniform float uTime;
uniform float uAmplitude;
uniform float uFrequency;

varying vec2 vUv;
varying vec3 vPosition;

void main() {
    vUv = uv;
    vPosition = position;

    // 基于时间和位置的波动效果
    vec3 newPosition = position;
    newPosition.z += sin(position.x * uFrequency + uTime) * uAmplitude;
    newPosition.z += cos(position.y * uFrequency + uTime) * uAmplitude;

    gl_Position = projectionMatrix * modelViewMatrix * vec4(newPosition, 1.0);
}
```

```glsl
// fragmentShader.glsl - 渐变色片段着色器
uniform float uTime;
uniform vec3 uColor1;
uniform vec3 uColor2;

varying vec2 vUv;
varying vec3 vPosition;

void main() {
    // 基于UV坐标的渐变
    vec3 color = mix(uColor1, uColor2, vUv.y);

    // 添加时间动画
    float pulse = sin(uTime * 2.0) * 0.5 + 0.5;
    color *= pulse;

    gl_FragColor = vec4(color, 1.0);
}
```

```typescript
// 使用自定义着色器
class CustomShaderMaterial {
  private material: THREE.ShaderMaterial;

  constructor() {
    this.material = new THREE.ShaderMaterial({
      vertexShader: vertexShaderSource,
      fragmentShader: fragmentShaderSource,
      uniforms: {
        uTime: { value: 0 },
        uAmplitude: { value: 0.5 },
        uFrequency: { value: 2.0 },
        uColor1: { value: new THREE.Color(0xff0000) },
        uColor2: { value: new THREE.Color(0x0000ff) }
      }
    });
  }

  update(time: number) {
    this.material.uniforms.uTime.value = time;
  }

  getMaterial() {
    return this.material;
  }
}
```

### 练习3：粒子系统实现 (第5-6周)

#### 目标
创建动态粒子系统，模拟知识点的动态效果。

#### 实现步骤
```typescript
class ParticleSystem {
  private particles: THREE.Points;
  private particleCount: number = 1000;
  private positions: Float32Array;
  private velocities: Float32Array;
  private colors: Float32Array;

  constructor(scene: THREE.Scene) {
    this.initParticles();
    scene.add(this.particles);
  }

  private initParticles() {
    const geometry = new THREE.BufferGeometry();

    // 初始化粒子属性
    this.positions = new Float32Array(this.particleCount * 3);
    this.velocities = new Float32Array(this.particleCount * 3);
    this.colors = new Float32Array(this.particleCount * 3);

    for (let i = 0; i < this.particleCount; i++) {
      const i3 = i * 3;

      // 随机位置
      this.positions[i3] = (Math.random() - 0.5) * 20;
      this.positions[i3 + 1] = (Math.random() - 0.5) * 20;
      this.positions[i3 + 2] = (Math.random() - 0.5) * 20;

      // 随机速度
      this.velocities[i3] = (Math.random() - 0.5) * 0.02;
      this.velocities[i3 + 1] = (Math.random() - 0.5) * 0.02;
      this.velocities[i3 + 2] = (Math.random() - 0.5) * 0.02;

      // 随机颜色
      const color = new THREE.Color();
      color.setHSL(Math.random(), 0.7, 0.5);
      this.colors[i3] = color.r;
      this.colors[i3 + 1] = color.g;
      this.colors[i3 + 2] = color.b;
    }

    geometry.setAttribute('position', new THREE.BufferAttribute(this.positions, 3));
    geometry.setAttribute('color', new THREE.BufferAttribute(this.colors, 3));

    const material = new THREE.PointsMaterial({
      size: 0.1,
      vertexColors: true,
      transparent: true,
      opacity: 0.8
    });

    this.particles = new THREE.Points(geometry, material);
  }

  update() {
    const positions = this.particles.geometry.attributes.position.array as Float32Array;

    for (let i = 0; i < this.particleCount; i++) {
      const i3 = i * 3;

      // 更新位置
      positions[i3] += this.velocities[i3];
      positions[i3 + 1] += this.velocities[i3 + 1];
      positions[i3 + 2] += this.velocities[i3 + 2];

      // 边界检测
      if (Math.abs(positions[i3]) > 10) this.velocities[i3] *= -1;
      if (Math.abs(positions[i3 + 1]) > 10) this.velocities[i3 + 1] *= -1;
      if (Math.abs(positions[i3 + 2]) > 10) this.velocities[i3 + 2] *= -1;
    }

    this.particles.geometry.attributes.position.needsUpdate = true;
  }
}
```

### 练习4：图算法实现 (第7-8周)

#### 目标
实现DAG算法和节点布局算法。

#### 实现步骤
```typescript
// 图数据结构
interface GraphNode {
  id: string;
  name: string;
  children: string[];
  parents: string[];
  level?: number;
  position?: THREE.Vector3;
}

class GraphLayoutAlgorithm {
  private nodes: Map<string, GraphNode> = new Map();
  private levels: GraphNode[][] = [];

  constructor(nodeData: GraphNode[]) {
    this.buildGraph(nodeData);
    this.calculateLevels();
    this.calculatePositions();
  }

  private buildGraph(nodeData: GraphNode[]) {
    // 构建节点映射
    nodeData.forEach(node => {
      this.nodes.set(node.id, { ...node });
    });

    // 建立父子关系
    nodeData.forEach(node => {
      node.children.forEach(childId => {
        const childNode = this.nodes.get(childId);
        if (childNode && !childNode.parents.includes(node.id)) {
          childNode.parents.push(node.id);
        }
      });
    });
  }

  private calculateLevels() {
    const visited = new Set<string>();
    const queue: string[] = [];

    // 找到根节点（没有父节点的节点）
    this.nodes.forEach((node, id) => {
      if (node.parents.length === 0) {
        queue.push(id);
        node.level = 0;
      }
    });

    // BFS计算层级
    while (queue.length > 0) {
      const currentId = queue.shift()!;
      const currentNode = this.nodes.get(currentId)!;
      visited.add(currentId);

      // 确保levels数组有足够的层级
      while (this.levels.length <= currentNode.level!) {
        this.levels.push([]);
      }
      this.levels[currentNode.level!].push(currentNode);

      // 处理子节点
      currentNode.children.forEach(childId => {
        const childNode = this.nodes.get(childId);
        if (childNode && !visited.has(childId)) {
          childNode.level = Math.max(
            childNode.level || 0,
            currentNode.level! + 1
          );

          // 检查是否所有父节点都已访问
          const allParentsVisited = childNode.parents.every(parentId =>
            visited.has(parentId)
          );

          if (allParentsVisited) {
            queue.push(childId);
          }
        }
      });
    }
  }

  private calculatePositions() {
    const levelSpacing = 5; // 层级间距
    const nodeSpacing = 3;  // 节点间距

    this.levels.forEach((levelNodes, levelIndex) => {
      const y = -levelIndex * levelSpacing;
      const nodeCount = levelNodes.length;

      if (nodeCount === 1) {
        // 单个节点居中
        levelNodes[0].position = new THREE.Vector3(0, y, 0);
      } else {
        // 多个节点圆形分布
        levelNodes.forEach((node, nodeIndex) => {
          const angle = (nodeIndex / nodeCount) * Math.PI * 2;
          const radius = Math.max(nodeCount * nodeSpacing / (2 * Math.PI), 2);

          const x = Math.cos(angle) * radius;
          const z = Math.sin(angle) * radius;

          node.position = new THREE.Vector3(x, y, z);
        });
      }
    });
  }

  getNodes(): GraphNode[] {
    return Array.from(this.nodes.values());
  }

  getLevels(): GraphNode[][] {
    return this.levels;
  }
}
```

## 项目扩展方向

### 1. 高级视觉效果
- **后处理效果**: 辉光、景深、SSAO
- **粒子特效**: 连接线粒子流、节点爆炸效果
- **动态材质**: 基于数据的材质变化

### 2. 交互功能增强
- **多选操作**: 框选、批量操作
- **拖拽编辑**: 节点位置调整、连线编辑
- **手势控制**: 触摸屏支持、手势识别

### 3. 数据可视化扩展
- **时间轴**: 知识演进过程可视化
- **统计图表**: 3D柱状图、饼图集成
- **热力图**: 学习热度可视化

### 4. 性能优化进阶
- **GPU计算**: 使用计算着色器
- **流式加载**: 大数据集分块加载
- **缓存策略**: 智能缓存管理

## 学习成果检验

### 阶段性测试

#### 第一阶段测试 (基础知识)
1. 创建一个包含10个几何体的3D场景
2. 实现基本的相机控制和光照
3. 添加简单的动画效果

#### 第二阶段测试 (进阶技术)
1. 编写自定义着色器实现特殊效果
2. 创建粒子系统模拟动态效果
3. 实现基本的用户交互功能

#### 第三阶段测试 (项目实战)
1. 实现完整的图算法和布局
2. 创建可交互的知识图谱可视化
3. 添加动画和性能优化

### 最终项目要求
创建一个功能完整的知识图谱可视化系统，包含：
- 数据加载和处理
- 3D可视化渲染
- 用户交互功能
- 动画效果
- 性能优化

通过系统学习这些内容，你将能够深入理解Three.js知识图谱可视化系统的实现原理，并具备进行二次开发的能力。建议按照学习路径循序渐进，多动手实践，逐步掌握3D可视化开发的核心技能。
