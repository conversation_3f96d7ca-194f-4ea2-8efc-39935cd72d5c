<script setup lang="ts">
// import { MODE, type Options } from "@endlessorigin/KsgMap";
import { KsgMap, MODE, type Options } from "./components/ksgMap";
import { ref, onMounted } from "vue";
import Stats from "three/examples/jsm/libs/stats.module.js";
import testAPI, {
  multiplyRootsAPI,
  localModeApi,
  localMode2Api,
} from "./network/api";

const request = localMode2Api;
const requestChampion = multiplyRootsAPI;
//模拟一个领域的数据
const container = ref<HTMLElement>();
const stats = new Stats();
const ksgRef = ref<any>();
let loading = ref<"loading" | "loaded" | "error">("loading");

const dataList = [
  {
    pointId: "K1907350322875195392",
    pointName: "Java",
    status: 0,
    isMilestone: null,
    parentPointIds: ["K1907349828001849331"],
  },
  {
    pointId: "K1907350322875195391",
    pointName: "JavaSE",
    status: 0,
    isMilestone: null,
    parentPointIds: ["K1907349828001849331", "K1907350322875195392"],
  },
  {
    pointId: "K19073498280018493401",
    pointName: "qwer",
    status: 0,
    isMilestone: null,
    parentPointIds: ["K1907349828001849331"],
  },
  {
    pointId: "K19073498280018493402",
    pointName: "dfg",
    status: 0,
    isMilestone: null,
    parentPointIds: ["K1907349828001849331"],
  },
  {
    pointId: "K19073498280018493403",
    pointName: "bca",
    status: 0,
    isMilestone: null,
    parentPointIds: ["K1907349828001849331"],
  },
  {
    pointId: "K19073498280018493404",
    pointName: "zxc",
    status: 0,
    isMilestone: null,
    parentPointIds: ["K1907349828001849331"],
  },
  {
    pointId: "K1907349828001849340",
    pointName: "ABC",
    status: 0,
    isMilestone: null,
    parentPointIds: ["K1907349828001849331"],
  },
  {
    pointId: "K1907349828001849350",
    pointName: "ksgmap",
    status: 1,
    isMilestone: null,
    parentPointIds: ["K1907349828001849331"],
  },
  {
    pointId: "K1907349828001849350",
    pointName: "ksgmap",
    status: 1,
    isMilestone: null,
    parentPointIds: ["K1907349828001849331"],
  },
  {
    pointId: "K19073498280018493510",
    pointName: "bac",
    status: 1,
    isMilestone: null,
    parentPointIds: ["K1907349828001849331"],
  },
  {
    pointId: "K19073498280018439350",
    pointName: "789",
    status: 1,
    isMilestone: null,
    parentPointIds: ["K1907349828001849331", "K1907350322875195392"],
  },
  {
    pointId: "K19073498280401849350",
    pointName: "456",
    status: 1,
    isMilestone: null,
    parentPointIds: ["K1907349828001849331", "K1907350322875195392"],
  },
  {
    pointId: "K19073498280301849350",
    pointName: "123",
    status: 1,
    isMilestone: null,
    parentPointIds: ["K1907349828001849331", "K1907350322875195392"],
  },
  {
    pointId: "K1907349828001849331",
    pointName: "知识图谱",
    status: 0,
    isMilestone: null,
    parentPointIds: ["K1907349828001849341"],
  },
  {
    pointId: "K1907349828001849332",
    pointName: "$x^2$",
    status: 0,
    isMilestone: null,
    parentPointIds: ["K1907349828001849341"],
  },
];
// 场景配置
const config: Options = {
  model: MODE.MULTIPLE_ROOT, //多根节点模式
  // 配置分层加载
  pointsLevelPager: {
    current: 1, //当前层
    levelSize: 1, //获取多少层
  },
};
// const config: Options = {
//   model: MODE.Single_ROOT, //单根节点模式
//   // 配置分层加载
//   pointsLevelPager: {
//     current: 1, //当前层
//     levelSize: 1, //获取多少层
//   },
// };
const total = dataList.length;
async function init() {
  stats.showPanel(0);
  stats.dom.style.position = "absolute";
  stats.dom.style.transform = "scale(2)";
  stats.dom.style.zIndex = "10";
  stats.dom.style.top = "30px";
  stats.dom.style.left = "40px";
  container.value?.appendChild(stats.dom);
  update();

  loading.value = "loading";
  // const {
  //   // @ts-ignore
  //   data: { records, total },
  //   /**
  //    * 这里测试只获取一层单个根节点
  //    */
  // } = await requestChampion(0, 10, "P1935945912330551296", 812);

  // ksgRef.value?.firstLoadPointsData(records, total);
  ksgRef.value?.firstLoadPointsData(dataList, total);
  // ksgRef.value?.firstLoadPointsData(dataList, total, "K1907349828001849331");

  loading.value = "loaded";
}

function update() {
  requestAnimationFrame(update);
  stats.update();
}
onMounted(init);
async function handleLoadMore(rootId: string, crt: number, levelSize: number) {
  loading.value = "loading";
  const {
    // @ts-ignore
    data: { dataList, total },
  } = await request(crt, levelSize, rootId);
  ksgRef.value?.loadMorePointsData(dataList);
  loading.value = "loaded";
}
function handleClickLabel(id: string) {
  // console.log("handleClickLabel 被触发，节点ID:", id);
  // window.open(`https://www.endlessorigin.com/klgdetail?klgCode=${id}`);
  alert(`点击了标签，节点ID: ${id}`);
}
</script>

<template>
  <div class="container" ref="container">
    <!-- @vue-ignore -->
    <KsgMap
      ref="ksgRef"
      :config="config"
      @load-more="handleLoadMore"
      :loading="loading"
      @click-label="handleClickLabel"
    />
  </div>
</template>

<style scoped>
.container {
  width: 100%;
  height: 100vh;
  display: flex;
  justify-content: center;
  align-items: center;
  position: relative;
}
</style>
