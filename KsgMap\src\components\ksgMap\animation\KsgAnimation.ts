import { Tween } from "@tweenjs/tween.js";

type AnimationParams = { string: number };

type AddAnimationOption = {
  from: object;
  to: { string: number };
  updateCallback: (animationParams: AnimationParams) => void;
  duration: number;
};
// const option:AddAnimationOption = {

// }
/**
 *该类是为了高效的组织动画提高渲染效率
 */
export class KsgAnimation {
  private tweenPool: Tween<any>[] = [];
  constructor() {}

  /**
   * 添加一个动画
   */
  addAnimation(options: AddAnimationOption) {
    const tween = new Tween(options.from);
  }

  /**
   * 更新函数
   * @param {number} time 当前时间
   */
  update(time: number) {
    for (let i = this.tweenPool.length - 1; i >= 0; i--) {
      const tween = this.tweenPool[i];
      if (!tween.update(time)) {
        this.tweenPool.splice(i, 1); // 动画完成后移除
      }
    }
  }

  /**
   * 暂停函数
   */
  pause() {}

  /**
   * 恢复函数
   */
  resume() {}
}
