# Three.js 学习指南 - 基础篇

## 第一步：理解 Three.js 核心架构

### 核心三要素
Three.js 的基本架构可以用一个简单的公式表示：
**Scene（场景） + Camera（相机） + Renderer（渲染器） = 3D 世界**

```javascript
// 最基本的 Three.js 程序结构
const scene = new THREE.Scene();        // 场景：3D世界的容器
const camera = new THREE.PerspectiveCamera(); // 相机：观察者的视角
const renderer = new THREE.WebGLRenderer();   // 渲染器：将3D转换为2D图像

// 渲染循环
function animate() {
    requestAnimationFrame(animate);
    renderer.render(scene, camera);
}
```

### 在 KsgMap 中的体现
查看 `src/components/ksgMap/config/` 目录，您会发现：
- `scene.ts` - 创建和配置场景
- `camera.ts` - 创建和配置相机  
- `renderer.ts` - 创建和配置渲染器

## 第二步：场景系统 (Scene)

### 基本概念
场景是所有3D对象的容器，就像一个舞台。

```javascript
const scene = new THREE.Scene();

// 添加对象到场景
scene.add(mesh);     // 添加网格对象
scene.add(light);    // 添加光源
scene.add(group);    // 添加组对象
```

### KsgMap 中的应用
```javascript
// src/components/ksgMap/config/scene.ts
const scene = new Scene();
scene.environment = texture;        // 环境贴图
scene.background = texture;         // 背景纹理
scene.backgroundIntensity = 0.02;   // 背景强度

const group = new Group();          // 创建组容器
group.position.set(...config.groupPosition);
scene.add(group);                   // 将组添加到场景
```

**学习要点：**
- Scene 是根容器
- Group 用于组织多个对象
- 环境贴图影响整体光照效果

## 第三步：相机系统 (Camera)

### 透视相机 (PerspectiveCamera)
模拟人眼视觉，有近大远小的透视效果。

```javascript
const camera = new THREE.PerspectiveCamera(
    75,        // fov: 视野角度
    width/height, // aspect: 宽高比
    0.1,       // near: 近裁剪面
    1000       // far: 远裁剪面
);
```

### KsgMap 中的配置
```javascript
// src/components/ksgMap/config/camera.ts
const camera = new PerspectiveCamera(
    config.fov,     // 45度视野角度
    config.aspect,  // 根据画布尺寸计算
    config.near,    // 0.1 近裁剪面
    config.far      // 1000 远裁剪面
);

// 设置相机位置
camera.position.set(30.2, -3.14, 24.98);
```

**学习要点：**
- fov 决定视野范围，类似镜头焦距
- aspect 必须与画布比例匹配
- near/far 定义可见范围，影响深度精度

## 第四步：渲染器系统 (Renderer)

### WebGL 渲染器
负责将3D场景渲染到2D画布上。

```javascript
const renderer = new THREE.WebGLRenderer({
    antialias: true,  // 抗锯齿
    alpha: true       // 透明背景
});

renderer.setSize(width, height);
renderer.setPixelRatio(window.devicePixelRatio);
```

### KsgMap 中的双渲染器架构
```javascript
// WebGL 渲染器 - 渲染3D对象
const renderer = new THREE.WebGLRenderer({
    antialias: true
});

// CSS2D 渲染器 - 渲染HTML标签
const css2dRenderer = new CSS2DRenderer();
```

**学习要点：**
- WebGLRenderer 处理3D图形
- CSS2DRenderer 处理HTML元素在3D空间中的定位
- 抗锯齿提升视觉质量但消耗性能

## 第五步：几何体和材质

### 几何体 (Geometry)
定义物体的形状和结构。

```javascript
// 基础几何体
const geometry = new THREE.BoxGeometry(1, 1, 1);
const geometry = new THREE.SphereGeometry(1, 32, 32);

// 缓冲几何体（高性能）
const geometry = new THREE.BufferGeometry();
```

### 材质 (Material)
定义物体的外观和光照响应。

```javascript
// 基础材质
const material = new THREE.MeshBasicMaterial({ color: 0xff0000 });

// 着色器材质（自定义效果）
const material = new THREE.ShaderMaterial({
    vertexShader: vertexShaderCode,
    fragmentShader: fragmentShaderCode,
    uniforms: { ... }
});
```

### KsgMap 中的应用
```javascript
// src/components/ksgMap/core/KsgPoints.ts
const pGeo = new BufferGeometry();  // 高性能几何体

// 自定义着色器材质
const material = new ShaderMaterial({
    uniforms: {
        map: { value: starTexture },
        uTime: { value: 0 }
    },
    vertexShader: vertShader,
    fragmentShader: fragShader,
    transparent: true,
    blending: AdditiveBlending  // 加法混合创建发光效果
});
```

## 第六步：网格对象 (Mesh)

### 基本概念
网格 = 几何体 + 材质

```javascript
const mesh = new THREE.Mesh(geometry, material);
scene.add(mesh);

// 变换操作
mesh.position.set(x, y, z);    // 位置
mesh.rotation.set(x, y, z);    // 旋转
mesh.scale.set(x, y, z);       // 缩放
```

### KsgMap 中的特殊对象
```javascript
// Points 对象 - 用于渲染大量点
const points = new THREE.Points(geometry, material);

// LineSegments 对象 - 用于渲染线段
const lines = new THREE.LineSegments(geometry, material);
```

## 第七步：动画和渲染循环

### 基本渲染循环
```javascript
function animate() {
    requestAnimationFrame(animate);
    
    // 更新对象状态
    mesh.rotation.x += 0.01;
    
    // 渲染场景
    renderer.render(scene, camera);
}
animate();
```

### KsgMap 中的渲染循环
```javascript
// src/components/ksgMap/hooks/useRendererFrame.ts
function startRenderFrame(time = 0) {
    const deltaTime = clock.getDelta();
    
    // CSS2D标签渲染
    ctx.css2dRenderer?.render(ctx.scene!, ctx.camera!);
    
    // WebGL场景渲染
    ctx.renderer?.render(ctx.scene!, ctx.camera!);
    
    // 控制器更新
    ctx.controls?.update(deltaTime);
    
    // 动画更新
    TWEEN.update(time);
    
    // 下一帧
    requestAnimationFrame(startRenderFrame);
}
```

## 第八步：事件和交互

### 鼠标交互基础
```javascript
// 射线投射 - 检测鼠标点击的3D对象
const raycaster = new THREE.Raycaster();
const mouse = new THREE.Vector2();

function onMouseClick(event) {
    // 将鼠标坐标转换为标准化设备坐标
    mouse.x = (event.clientX / window.innerWidth) * 2 - 1;
    mouse.y = -(event.clientY / window.innerHeight) * 2 + 1;
    
    // 从相机发射射线
    raycaster.setFromCamera(mouse, camera);
    
    // 检测相交对象
    const intersects = raycaster.intersectObjects(scene.children);
    if (intersects.length > 0) {
        console.log('点击了对象:', intersects[0].object);
    }
}
```

### KsgMap 中的交互系统
KsgMap 有复杂的交互系统，包括：
- 节点悬停检测
- 点击聚焦功能
- 相机控制器
- 双击切换视角

## 学习建议

### 1. 动手实践
创建一个简单的 Three.js 项目：

```html
<!DOCTYPE html>
<html>
<head>
    <title>My First Three.js Scene</title>
    <style>
        body { margin: 0; }
        canvas { display: block; }
    </style>
</head>
<body>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/three.js/r128/three.min.js"></script>
    <script>
        // 创建场景、相机、渲染器
        const scene = new THREE.Scene();
        const camera = new THREE.PerspectiveCamera(75, window.innerWidth/window.innerHeight, 0.1, 1000);
        const renderer = new THREE.WebGLRenderer();
        
        renderer.setSize(window.innerWidth, window.innerHeight);
        document.body.appendChild(renderer.domElement);
        
        // 创建一个立方体
        const geometry = new THREE.BoxGeometry();
        const material = new THREE.MeshBasicMaterial({ color: 0x00ff00 });
        const cube = new THREE.Mesh(geometry, material);
        scene.add(cube);
        
        camera.position.z = 5;
        
        // 渲染循环
        function animate() {
            requestAnimationFrame(animate);
            cube.rotation.x += 0.01;
            cube.rotation.y += 0.01;
            renderer.render(scene, camera);
        }
        animate();
    </script>
</body>
</html>
```

### 2. 理解坐标系
Three.js 使用右手坐标系：
- X轴：红色，向右为正
- Y轴：绿色，向上为正  
- Z轴：蓝色，向外为正

### 3. 掌握向量和矩阵
```javascript
// 向量操作
const vector = new THREE.Vector3(1, 2, 3);
vector.add(new THREE.Vector3(1, 1, 1));  // 向量加法
vector.normalize();                       // 单位化

// 矩阵变换
const matrix = new THREE.Matrix4();
matrix.makeTranslation(1, 2, 3);         // 平移矩阵
```

# Three.js 入门指南 - 为 KsgMap 组件学习者准备

## 什么是 Three.js？

Three.js 是一个基于 WebGL 的 JavaScript 3D 图形库，它简化了在网页中创建和显示 3D 图形的过程。

### 核心概念

#### 1. 场景 (Scene) 🎬
场景就像一个 3D 世界的舞台，所有的 3D 对象都放在这个舞台上。

```javascript
const scene = new THREE.Scene();
```

**在 KsgMap 中的应用**：
- `config/scene.ts` 创建知识图谱的 3D 世界
- 设置背景图片和环境光照
- 创建容器组来组织知识节点

#### 2. 相机 (Camera) 📷
相机定义了我们从哪个角度观察 3D 世界，就像现实中的摄像机。

```javascript
const camera = new THREE.PerspectiveCamera(
  75,           // 视野角度 (FOV)
  width/height, // 宽高比
  0.1,          // 近裁剪面
  1000          // 远裁剪面
);
```

**参数解释**：
- **FOV (视野角度)**：数值越大，看到的范围越广，但物体显得越小
- **宽高比**：必须与画布尺寸匹配，否则会变形
- **近/远裁剪面**：定义相机能看到的距离范围

**在 KsgMap 中的应用**：
- `config/camera.ts` 设置观察知识图谱的视角
- 45度 FOV 提供自然的视觉效果
- 根据图谱大小调整观察距离

#### 3. 渲染器 (Renderer) 🖼️
渲染器负责将 3D 场景"拍照"并显示在网页上。

```javascript
const renderer = new THREE.WebGLRenderer();
renderer.setSize(width, height);
document.body.appendChild(renderer.domElement);
```

**在 KsgMap 中的应用**：
- `config/renderer.ts` 创建 WebGL 渲染器
- `config/css2dRenderer.ts` 创建 HTML 标签渲染器
- 双渲染器配合：3D 图形 + HTML 文字

#### 4. 几何体 (Geometry) 📐
几何体定义了 3D 对象的形状，包含顶点、面等信息。

```javascript
const geometry = new THREE.BoxGeometry(1, 1, 1); // 立方体
const geometry = new THREE.SphereGeometry(1);    // 球体
```

**在 KsgMap 中的应用**：
- `core/KsgPoints.ts` 使用点几何体渲染知识节点
- `core/KsgLine.ts` 使用线几何体渲染节点连接

#### 5. 材质 (Material) 🎨
材质定义了 3D 对象的外观，包括颜色、纹理、光照响应等。

```javascript
const material = new THREE.MeshBasicMaterial({ color: 0xff0000 });
```

**在 KsgMap 中的应用**：
- 使用自定义着色器材质实现特殊效果
- 节点发光效果、连线流光动画

#### 6. 网格 (Mesh) 🕸️
网格是几何体和材质的组合，形成最终可见的 3D 对象。

```javascript
const mesh = new THREE.Mesh(geometry, material);
scene.add(mesh);
```

## 基础渲染流程

### 1. 创建基本场景
```javascript
// 1. 创建场景
const scene = new THREE.Scene();

// 2. 创建相机
const camera = new THREE.PerspectiveCamera(75, window.innerWidth/window.innerHeight, 0.1, 1000);

// 3. 创建渲染器
const renderer = new THREE.WebGLRenderer();
renderer.setSize(window.innerWidth, window.innerHeight);
document.body.appendChild(renderer.domElement);

// 4. 创建一个立方体
const geometry = new THREE.BoxGeometry();
const material = new THREE.MeshBasicMaterial({ color: 0x00ff00 });
const cube = new THREE.Mesh(geometry, material);
scene.add(cube);

// 5. 设置相机位置
camera.position.z = 5;

// 6. 渲染
renderer.render(scene, camera);
```

### 2. 添加动画循环
```javascript
function animate() {
  requestAnimationFrame(animate);
  
  // 旋转立方体
  cube.rotation.x += 0.01;
  cube.rotation.y += 0.01;
  
  // 渲染场景
  renderer.render(scene, camera);
}
animate();
```

## KsgMap 中的 Three.js 应用

### 1. 高性能节点渲染
KsgMap 使用 `Points` 类而不是多个 `Mesh`：

```javascript
// 传统方式（性能差）
nodes.forEach(node => {
  const geometry = new THREE.SphereGeometry(0.1);
  const material = new THREE.MeshBasicMaterial({ color: node.color });
  const mesh = new THREE.Mesh(geometry, material);
  scene.add(mesh);
});

// KsgMap 方式（高性能）
const geometry = new THREE.BufferGeometry();
const positions = new Float32Array(nodes.length * 3);
const colors = new Float32Array(nodes.length * 3);

nodes.forEach((node, i) => {
  positions[i * 3] = node.x;
  positions[i * 3 + 1] = node.y;
  positions[i * 3 + 2] = node.z;
  
  colors[i * 3] = node.color.r;
  colors[i * 3 + 1] = node.color.g;
  colors[i * 3 + 2] = node.color.b;
});

geometry.setAttribute('position', new THREE.BufferAttribute(positions, 3));
geometry.setAttribute('color', new THREE.BufferAttribute(colors, 3));

const material = new THREE.PointsMaterial({ size: 2, vertexColors: true });
const points = new THREE.Points(geometry, material);
scene.add(points);
```

### 2. 自定义着色器
KsgMap 使用 GLSL 着色器实现特殊效果：

```javascript
const material = new THREE.ShaderMaterial({
  uniforms: {
    uTime: { value: 0.0 },
    uColor: { value: new THREE.Color(0xff0000) }
  },
  vertexShader: `
    void main() {
      gl_Position = projectionMatrix * modelViewMatrix * vec4(position, 1.0);
      gl_PointSize = 10.0;
    }
  `,
  fragmentShader: `
    uniform float uTime;
    uniform vec3 uColor;
    void main() {
      float distance = length(gl_PointCoord - vec2(0.5));
      if (distance > 0.5) discard;
      gl_FragColor = vec4(uColor, 1.0 - distance);
    }
  `
});
```

### 3. 相机控制
KsgMap 扩展了 OrbitControls：

```javascript
import { OrbitControls } from 'three/examples/jsm/controls/OrbitControls';

const controls = new OrbitControls(camera, renderer.domElement);
controls.enableDamping = true;  // 启用阻尼
controls.dampingFactor = 0.05;  // 阻尼系数
controls.minDistance = 1;       // 最小距离
controls.maxDistance = 100;     // 最大距离
```

## 常用工具和技巧

### 1. 坐标系统
Three.js 使用右手坐标系：
- X 轴：左右方向（正值向右）
- Y 轴：上下方向（正值向上）
- Z 轴：前后方向（正值向外）

### 2. 单位和比例
Three.js 中的单位是抽象的，1 个单位可以代表 1 米、1 厘米等，关键是保持一致性。

### 3. 性能优化
- 使用 `BufferGeometry` 而不是 `Geometry`
- 合并相似的几何体
- 使用实例化渲染 `InstancedMesh`
- 启用视锥体剔除

### 4. 调试工具
```javascript
// 添加坐标轴辅助器
const axesHelper = new THREE.AxesHelper(5);
scene.add(axesHelper);

// 添加网格辅助器
const gridHelper = new THREE.GridHelper(10, 10);
scene.add(gridHelper);
```
