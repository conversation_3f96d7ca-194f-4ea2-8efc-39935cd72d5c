// 导入节点克隆工具函数，用于创建节点的深拷贝
import clonePoint from "../utils/clonePoint";
// 导入类型定义：PointData为原始数据，Point为处理后的节点对象
import type { PointData, Point } from "../types";
// 导入帧调度器，用于将耗时计算分散到多个帧中执行，避免阻塞UI
import FrameScheduler from "../utils/FrameScheduler";

// 创建全局任务调度器实例 - 管理异步计算任务
// 通过分帧执行避免长时间的同步计算阻塞主线程，确保UI响应流畅
export const frameScheduler = new FrameScheduler();

/**
 * 节点变化数据类型 - 用于记录节点在更新过程中的状态变化
 * 主要用于增量更新和动画过渡效果
 */
export type ModifyPoint = {
  /** 更新前的节点状态 - 保存原始位置和属性 */
  old: Point;
  /** 更新后的节点状态 - 保存新的位置和属性（可选） */
  new?: Point;
};

/**
 * 差异数据类型 - 用于描述数据更新时的变化情况
 * 支持增量更新，避免重新计算整个图结构
 */
export type DiffData = {
  /** 新增的节点列表 - 首次加载或新增的节点 */
  newPoints: Point[];
  /** 位置发生变化的节点映射 - key为节点ID，value为变化前后的状态 */
  updatePoints: Map<string, ModifyPoint>;
};

/**
 * 知识图谱空间布局计算器 - KsgGraph类
 *
 * 这是知识图谱的核心计算引擎，负责将原始的节点数据转换为3D空间中的坐标布局
 * 实现了DAG（有向无环图）的层次化布局算法，确保知识节点按照逻辑关系有序排列
 *
 * 主要功能：
 * - 解析节点间的父子关系，构建图结构
 * - 计算节点的层级分布（BFS层次遍历）
 * - 计算每个节点在3D空间中的精确坐标
 * - 支持增量数据加载和位置更新
 * - 提供异步计算能力，避免阻塞UI线程
 *
 * 算法特点：
 * - 使用广度优先搜索确定节点层级
 * - 同层节点均匀分布，避免重叠
 * - 支持多根节点的复杂图结构
 * - 优化的增量更新算法，提高性能
 */
export default class KsgGraph {
  /**
   * 节点数据映射表 - 存储所有处理后的节点信息
   * key: 节点ID, value: 包含坐标和关系信息的Point对象
   */
  pointsData: Map<string, Point> = new Map();

  /**
   * 层级索引映射 - 按层级组织节点ID
   * key: 层级编号(0,1,2...), value: 该层级的所有节点ID数组
   * 用于快速查找同层节点和层级遍历
   */
  idLevelMap: { [level: number]: string[] } = {};

  /**
   * 增量更新的差异数据 - 记录数据变化情况
   * 用于支持懒加载和动画过渡效果
   */
  diffData: DiffData = { newPoints: [], updatePoints: new Map() };

  /**
   * 构造函数 - 初始化知识图谱布局计算器
   * 接收原始节点数据并立即开始计算布局
   *
   * @param pointsData 原始节点数据数组，包含节点ID、名称、父子关系等信息
   */
  constructor(pointsData: PointData[]) {
    // 立即启动计算流程，将原始数据转换为3D空间布局
    this.compute(pointsData);
  }

  /**
   * 计算节点布局的主流程函数
   * 按照标准的图布局算法流程执行：构建图 -> 计算层级 -> 计算坐标
   *
   * 执行步骤：
   * 1. 构建图结构（解析节点关系，建立父子映射）
   * 2. 计算节点层级（使用BFS算法确定每个节点的层级位置）
   * 3. 计算节点3D坐标（异步执行，避免阻塞UI线程）
   *
   * @param pointsData 原始节点数据数组
   */
  compute(pointsData: PointData[]) {
    // 第一步：构建图数据结构
    // 将原始的PointData转换为内部的Point对象，并建立完整的父子关系映射
    this.build(pointsData);

    // 第二步：计算节点层级
    // 使用广度优先搜索(BFS)算法确定每个节点在图中的层级深度
    this.computeLevel(this.pointsData);

    // 第三步：异步计算节点的3D空间坐标
    // 使用帧调度器将计算任务分散到多个帧中，防止长时间计算阻塞UI
    frameScheduler.addTask(() => {
      this.computePointPosition(); // 执行坐标计算
      return false; // 返回false表示这是最后一个任务，完成后停止
    });
  }
  /**
   * 加载更多数据的增量更新函数
   * 支持在现有图结构基础上添加新节点，并处理位置变化
   *
   * @param pointsData 新增的节点数据数组
   * @returns Promise<DiffData> 返回包含新增和更新节点信息的差异数据
   */
  loadMore(pointsData: PointData[]): Promise<DiffData> {
    // 初始化差异数据结构，用于记录本次更新的变化
    const diffData: DiffData = {
      newPoints: [], // 全新添加的节点
      updatePoints: new Map(), // 位置发生变化的现有节点
    };
    // 将差异数据保存到实例属性中，供其他方法使用
    this.diffData = diffData;

    // 预处理：检查新数据中哪些节点已存在，并为它们创建更新记录
    pointsData
      .map((point) => {
        // 查找现有节点
        let oldPoint = this.pointsData.get(point.pointId)!;
        if (oldPoint) {
          // 如果节点已存在，克隆当前状态作为"更新前"的记录
          oldPoint = clonePoint(oldPoint);
          diffData.updatePoints.set(oldPoint.id, {
            old: oldPoint, // 保存更新前的状态
          });
        }
        return point?.pointId ?? null; // 返回节点ID或null
      })
      .filter((point) => point); // 过滤掉null值

    // 执行数据处理流程（与初始化相同）
    this.build(pointsData); // 构建图结构
    this.computeLevel(this.pointsData); // 重新计算层级

    // 异步计算节点位置
    frameScheduler.addTask(() => {
      this.computePointPosition();
      return false; // 任务完成标志
    });

    // 返回Promise，在位置计算完成后resolve差异数据
    return new Promise((resolve) => {
      frameScheduler.onCompleted(() => {
        // 遍历所有新增的节点，分类到新增或更新列表中
        pointsData.forEach(({ pointId }) => {
          if (diffData.updatePoints.has(pointId)) {
            // 如果是更新的节点，保存"更新后"的状态
            diffData.updatePoints.get(pointId)!.new =
              this.pointsData.get(pointId)!;
          } else {
            // 如果是全新的节点，添加到新增列表中
            diffData.newPoints.push(this.pointsData.get(pointId)!);
          }
        });
        // 调试信息（可以移除）
        // console.log("diffData", diffData);
        resolve(diffData); // 返回完整的差异数据
      });
    });
  }

  /**
   * 构建图数据结构的私有方法
   * 将原始节点数据转换为内部的Point对象，并建立完整的父子关系网络
   *
   * 处理流程：
   * 1. 创建或更新Point对象
   * 2. 建立父子关系映射
   *
   * @param pointsData 原始节点数据数组
   */
  private build(pointsData: PointData[]) {
    // 第一个异步任务：创建或更新节点对象
    frameScheduler.addTask(() => {
      // 遍历所有输入的节点数据
      for (const point of pointsData) {
        let index = -1; // 初始索引设为-1，后续会被正确设置
        // 检查节点是否已存在
        const oldPoint = this.pointsData.get(point.pointId);

        if (oldPoint) {
          // 如果节点已存在，更新其父节点列表
          const cloneOldPoint = clonePoint(oldPoint);
          // 记录到差异数据中，用于动画处理
          this.diffData.updatePoints.set(point.pointId, {
            old: cloneOldPoint, // 保存更新前状态
          });
          // 合并新的父节点ID到现有节点
          oldPoint!.parentIds.push(...point.parentPointIds);
        } else {
          // 如果是新节点，创建完整的Point对象
          this.pointsData.set(point.pointId, {
            id: point.pointId, // 节点唯一标识
            name: point.pointName, // 节点显示名称
            parentIds: point.parentPointIds, // 父节点ID列表
            childIds: [], // 子节点ID列表（初始为空）
            level: -1, // 层级（初始为-1，后续计算）
            coordinate: [0, 0, 0], // 3D坐标（初始为原点）
            isMilestone: point.isMilestone, // 是否为里程碑节点
            status: point.status, // 节点状态
            index, // 节点索引
          });
        }
      }
      return false; // 任务完成
    });

    // 第二个异步任务：建立父子关系映射
    frameScheduler.addTask(() => {
      // 遍历所有节点，为每个父节点添加子节点引用
      for (const point of pointsData) {
        const { pointId, parentPointIds } = point;
        // 为每个父节点添加当前节点作为子节点
        for (const parentId of parentPointIds) {
          this.pointsData.get(parentId)?.childIds.push(pointId);
        }
      }
      return false; // 任务完成
    });
  }

  /**
   * 计算节点层级的方法
   * 使用拓扑排序算法（基于Kahn算法）计算DAG中每个节点的层级
   * 确保父节点总是在子节点的上层
   *
   * @param points 节点数据映射表
   */
  computeLevel(points: Map<string, Point>) {
    // 初始化层级映射和相关变量
    this.idLevelMap = {};
    let degrees: { [key: string]: number } = {}; // 每个节点的入度计数
    let queue: string[] = []; // BFS队列
    const scope = this; // 保存this引用

    // 注释掉的代码：原本考虑任务分块的实现
    // const taskCount = 10; // 分出的任务数量
    // frameScheduler.addTask(() => {
    //   return false;
    // });

    /**
     * 第一个异步任务：初始化入度计算
     * 统计每个节点作为子节点被引用的次数（入度）
     */
    frameScheduler.addTask(() => {
      // 重置所有状态变量
      scope.idLevelMap = {};
      degrees = {};
      queue = [];

      // 遍历所有节点，计算每个子节点的入度
      scope.pointsData.forEach((point) => {
        for (const childId of point.childIds) {
          if (!degrees[childId]) degrees[childId] = 0; // 初始化入度为0
          degrees[childId]++; // 子节点入度加1
        }

        return false; // 这里应该移除，因为forEach不需要返回值
      });

      return false; // 任务完成
    });

    // 第二个异步任务：找到所有根节点（入度为0的节点）
    let startLevel = 0; // 起始层级
    frameScheduler.addTask(() => {
      scope.pointsData.forEach((point) => {
        const { id } = point;
        if (!degrees[id]) {
          // 如果节点入度为0，说明是根节点
          queue.push(id); // 加入BFS队列
          point.level = startLevel; // 设置为第0层
          if (!this.idLevelMap[startLevel]) this.idLevelMap[startLevel] = [];
          this.idLevelMap[startLevel].push(id); // 添加到层级映射中
        }
      });

      return false; // 任务完成
    });

    // 第三个异步任务：执行BFS遍历，计算所有节点的层级
    frameScheduler.addTask(() => {
      // 使用BFS算法逐层计算节点层级
      while (queue.length) {
        const id = queue.shift()!; // 从队列中取出一个节点
        const point = scope.pointsData.get(id)!;

        // 处理当前节点的所有子节点
        for (const childId of point.childIds) {
          degrees[childId]--; // 子节点入度减1

          if (degrees[childId] === 0) {
            // 如果子节点入度变为0，说明其所有父节点都已处理
            const crtLevel = scope.pointsData.get(id)!.level + 1; // 层级 = 父节点层级 + 1
            points.get(childId)!.level = crtLevel; // 设置子节点层级
            queue.push(childId); // 加入BFS队列

            // 更新层级映射
            if (!scope.idLevelMap[crtLevel]) scope.idLevelMap[crtLevel] = [];
            scope.idLevelMap[crtLevel].push(childId);
          }
        }
      }

      // 环检测：如果还有节点的入度大于0，说明存在环
      if (Object.values(degrees).some((item) => item > 0)) {
        throw new Error("当前数据结构存在环");
      }
      return false; // 任务完成
    });
  }

  /**
   * 计算每层节点的3D空间位置
   * 使用同心圆布局算法，确保同层节点均匀分布，避免重叠
   *
   * @param levelHeight 层级间的垂直间距（默认15个单位）
   * @param pointSpace 同层节点间的径向间距（默认7个单位）
   */
  computePointPosition(levelHeight: number = 15, pointSpace: number = 7) {
    // 获取所有层级编号并转换为数字数组
    const levels = Object.keys(this.idLevelMap).map((key) => Number(key));

    // 为每个层级创建一个异步任务，避免长时间计算阻塞UI
    levels.forEach((level, index) => {
      frameScheduler.addTask(() => {
        // 获取当前层级的所有节点ID
        const crtLevelPointIds = this.idLevelMap[level];
        // 计算当前层级的Y坐标（负数，向下分布）
        let y = -level * levelHeight;

        // 当前层级的节点数量
        let count = crtLevelPointIds.length;

        if (count === 1) {
          // 单个节点直接放在中心位置
          const id = crtLevelPointIds[0];

          // 检查是否需要记录位置变化（用于动画）
          if (
            this.diffData.updatePoints.has(id) &&
            this.isPositionChange(id, [0, y, 0])
          ) {
            // 如果节点已在更新列表中且位置确实发生变化，记录新状态
            this.diffData.updatePoints.get(id)!.new = this.pointsData.get(id)!;
          } else if (this.isPositionChange(id, [0, y, 0])) {
            // 如果节点不在更新列表中但位置发生变化，创建更新记录
            this.diffData.updatePoints.set(id, {
              old: clonePoint(this.pointsData.get(id)!), // 保存旧状态
              new: this.pointsData.get(id)!, // 引用新状态
            });
          }
          // 设置节点的最终坐标
          this.pointsData.get(id)!.coordinate = [0, y, 0];
        } else {
          // 多个节点使用同心圆布局
          const interval = 3; // 每圈增加的节点数基数
          let index = 0; // 当前处理的节点索引

          // 分圈布局：内圈3个，第二圈6个，第三圈9个...
          for (let round = 1, s = count; s != 0; ++round) {
            // 计算当前圈应放置的节点数
            let num = interval * round;
            if (num < s) {
              s -= num; // 剩余节点数减去当前圈的节点数
            } else {
              num = s; // 最后一圈放置所有剩余节点
              s = 0; // 标记完成
            }

            // 根据圈数微调Y坐标，创建轻微的高度变化
            y += Math.sin(round);
            // 计算当前圈的半径
            const r = round * pointSpace;

            // 在当前圈上均匀分布节点
            for (let i = 0; i < num; ++i) {
              // 使用极坐标计算节点位置
              const x = r * Math.cos((2 * Math.PI * i) / num);
              const z = r * Math.sin((2 * Math.PI * i) / num);

              if (index < count) {
                const id = crtLevelPointIds[index++];

                // 检查并记录位置变化（与单节点逻辑相同）
                if (
                  this.diffData.updatePoints.has(id) &&
                  this.isPositionChange(id, [x, y, z])
                ) {
                  this.diffData.updatePoints.get(id)!.new =
                    this.pointsData.get(id)!;
                } else if (this.isPositionChange(id, [x, y, z])) {
                  this.diffData.updatePoints.set(id, {
                    old: clonePoint(this.pointsData.get(id)!),
                    new: this.pointsData.get(id)!,
                  });
                }
                // 设置节点的最终3D坐标
                this.pointsData.get(id)!.coordinate = [x, y, z];
              }
            }
          }
        }
        // 判断是否为最后一个层级任务
        return index === levels.length - 1;
      });
    });
  }

  /**
   * 检查节点位置是否发生变化的私有方法
   * 用于增量更新时判断哪些节点需要执行位置变化动画
   *
   * @param id 节点ID
   * @param coordinate 新的坐标位置 [x, y, z]
   * @returns boolean 如果位置发生变化返回true，否则返回false
   */
  private isPositionChange(id: string, coordinate: [number, number, number]) {
    // 获取节点当前的坐标，如果不存在则默认为原点
    const [x, y, z] = this.pointsData.get(id)?.coordinate ?? [0, 0, 0];

    // 特殊情况：如果新坐标或旧坐标都是原点，认为没有有效变化
    if (
      (!coordinate[0] && !coordinate[1] && !coordinate[2]) ||
      (!x && !y && !z)
    )
      return false;

    // 比较X、Y、Z坐标是否有任何一个发生变化
    return x !== coordinate[0] || y !== coordinate[1] || z !== coordinate[2];
  }

  /**
   * 根据节点ID获取Point对象的公共方法
   * 提供安全的节点查找功能，避免直接操作内部数据结构
   *
   * @param id 节点的唯一标识符
   * @returns Point对象或null（如果节点不存在）
   */
  getPointById(id: string): Point | null {
    return this.pointsData.get(id) ?? null;
  }

  /**
   * 获取当前图的最大层级深度
   * 用于确定图的整体结构深度，层级从0开始计算
   *
   * @returns number 最大层级编号（从0开始）
   */
  getMaxLevel() {
    return Math.max(...Object.keys(this.idLevelMap).map((key) => Number(key)));
  }
}
