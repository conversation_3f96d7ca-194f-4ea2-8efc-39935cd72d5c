import type { CameraConfig, RendererConfig, SceneConfig, ControlsConfig } from "../types/index";
import { MODE } from "../enums";
export declare function useInitThreeJsConfig(option?: Options): {
    cameraConfig: any;
    renderConfig: any;
    sceneConfig: any;
    controlsConfig: any;
    wrapperEleSizeConfig: {
        width: number;
        height: number;
    };
};
export type Options = {
    model?: MODE;
    /**视口范围 */
    viewRange?: {
        minX: number;
        maxX: number;
        minY: number;
        maxY: number;
    };
    camera?: CameraConfig;
    renderer?: RendererConfig;
    scene?: SceneConfig;
    controls?: ControlsConfig;
    pointsLevelPager?: {
        current: number;
        levelSize: number;
        total?: number;
    };
    levelSpace?: number;
    point?: {
        radius: number;
        space: number;
    };
    hoverLabel?: {
        offsetX: number;
        offsetY: number;
    };
    maxDistance?: number;
    pointSpace?: number;
    focusBackToRoot?: () => void;
    focusBack?: () => void;
};
