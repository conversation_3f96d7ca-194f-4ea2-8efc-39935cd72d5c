import { KsgControls } from "../core/KsgControls";
import type { Line2 } from "three/examples/jsm/lines/webgpu/Line2.js";
/**
 *
 * @param {OrbitControls } controls 相机控制器
 * @param {[number,number,number]} to 目标位置
 */
export declare function enterGlobalAnimation(controls: KsgControls, to: [number, number, number]): Promise<unknown>;
/**
 * 清除边动画
 * @param {Line2} line 需要清除的边
 */
export declare function clearLineAnimation(line: Line2): Promise<Line2>;
