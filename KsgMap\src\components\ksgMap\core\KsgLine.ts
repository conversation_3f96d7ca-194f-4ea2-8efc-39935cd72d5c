import {
  BufferGeometry,
  LineBasicMaterial,
  LineSegments,
  ShaderMaterial,
} from "three";
import { Point } from "../types";
import { pointStatusToColor } from "../utils";
import vertexShader from "../shader/lineVert.glsl?raw";
import fragmentShader from "../shader/lineFrag.glsl?raw";
import { GUI } from "three/examples/jsm/libs/lil-gui.module.min.js";
import ctx from "../ctx";

// 流光随机效果配置
const isRandom = {
  value: false, // 是否启用随机流光效果
};

/**
 * 知识节点连线渲染类 - KsgLine2
 *
 * 继承自Three.js的LineSegments类，专门用于渲染知识图谱中节点间的连接线
 * 使用自定义着色器实现流光动画效果，提供丰富的视觉反馈
 *
 * 主要功能：
 * - 渲染节点间的连接关系
 * - 实现流光动画效果，增强视觉表现
 * - 支持动态颜色和透明度变化
 * - 高性能的批量线条渲染
 * - 支持随机和同步的流光模式
 *
 * 技术特点：
 * - 使用BufferGeometry优化性能
 * - 自定义GLSL着色器实现流光效果
 * - 支持顶点颜色插值
 * - 透明混合模式创建柔和效果
 *
 * 流光动画原理：
 * - 通过着色器uniform变量控制流光位置
 * - 使用segmentProgress标识线段上的位置
 * - 动态调整透明度创建流动效果
 */
import { Float32BufferAttribute } from "three";
export default class KsgLine2 extends LineSegments {
  /** 流光动画开关 - 控制是否启用流光效果 */
  enableAnimation: boolean = false;

  /** 流光移动速度 - 控制流光动画的播放速度 */
  speed: number = 0.01;

  // === 缓存数组 - 用于动态添加和更新线条数据 ===
  /** 位置数据缓存 - 存储所有线条顶点的3D坐标 */
  posit: number[] = [];

  /** 颜色数据缓存 - 存储所有线条顶点的RGB颜色值 */
  colors: number[] = [];

  /** 索引数据缓存 - 存储线条的索引信息 */
  indexs: number[] = [];

  /** 线段进度缓存 - 存储每个顶点在线段上的位置比例(0-1) */
  segmentProgress: number[] = [];

  /** 随机数缓存 - 为每条线分配随机值，用于随机流光效果 */
  random: number[] = [];
  constructor(focusPoint: Point, focusChildren: Point[], opacity: number = 1) {
    const geo = new BufferGeometry();
    const colors: number[] = [];
    const indexs: number[] = [];
    const segmentProgress: number[] = [];
    const random: number[] = [];
    //位置
    const positions: number[] = [];
    const tempPosition: number[] = [];
    focusChildren.forEach((point, index) => {
      // 生成一个随机数-1.5到2.0
      // focusPoint.coordinate.forEach((v, i) => {
      //   tempPosition.push(v + Math.random() * 0.5 - 0.25);
      // });

      const randomNum = Number(Math.random() * 1.5);

      positions.push(...focusPoint.coordinate); //起始点（聚焦节点）
      tempPosition.push(...focusPoint.coordinate);
      colors.push(...pointStatusToColor(focusPoint.status)); //起始点颜色
      segmentProgress.push(0);
      random.push(randomNum);

      positions.push(...focusPoint.coordinate); //结束点
      tempPosition.push(...point.coordinate);
      colors.push(...pointStatusToColor(point.status)); //结束点颜色
      point.endPointsIndex = index * 2 + 1;
      segmentProgress.push(1);
      indexs.push(index);
      random.push(randomNum);
      // console.log("===>", point.id, randomNum);
    });

    // 传入顶点着色
    geo.setAttribute("position", new Float32BufferAttribute(positions, 3));
    geo.setAttribute("color", new Float32BufferAttribute(colors, 3));
    geo.setAttribute("lineIndex", new Float32BufferAttribute(indexs, 1));
    geo.setAttribute(
      "segmentProgress",
      new Float32BufferAttribute(segmentProgress, 1)
    );
    geo.setAttribute("random", new Float32BufferAttribute(random, 1));

    const material = new ShaderMaterial({
      vertexShader,
      fragmentShader,
      transparent: true,
      depthWrite: false,
      vertexColors: true,
      uniforms: {
        uOpacity: { value: opacity }, //除流光外区域的透明度
        uTime: { value: 0 }, //动画
        uWidth: { value: 0.4 }, //流光长度
        uSpeed: { value: 0.15 }, //流光速度
        uOpacityOffset: { value: 0.2 }, //流光透明度偏移量
        uProgress: { value: -0.3 },
        uIsRandom: { value: isRandom.value },
      },
    });

    super(geo, material);
    this.frustumCulled = false;
    this.posit = tempPosition;
    this.colors = colors;
    this.indexs = indexs;
    this.segmentProgress = segmentProgress;
    this.random = random;


    material.uniforms.uWidth.value = ctx.line?.length ?? 0.3;
    material.uniforms.uSpeed.value = ctx.line?.speed ?? 0.015;
    material.uniforms.uIsRandom.value = ctx.line?.isRandom ?? true;
    // const gui = new GUI();
    // gui.add(material.uniforms.uWidth, "value", 0, 2, 0.1).name("流光长度");
    // //@ts-ignore
    // gui.add(this, "speed", 0, 0.1, 0.01).name("流光速度");
    // gui
    //   .add(isRandom, "value")
    //   .onChange((value) => {
    //     material.uniforms.uIsRandom.value = value;
    //   })
    //   .name("是否随机");
  }

  /**
   * 释放内存
   */
  dispose() {
    this.geometry.dispose();
    (this.material as LineBasicMaterial).dispose();
  }
  /**
   * 末位置更新函数
   */
  updateEndPosition(index: number, position: [number, number, number]) {
    this.geometry.attributes.position.setXYZ(
      index,
      position[0],
      position[1],
      position[2]
    );
    // 更新缓存数据
    this.posit[index] = position[0];
    this.posit[index + 1] = position[1];
    this.posit[index + 2] = position[2];
    this.geometry.attributes.position.needsUpdate = true;
  }

  updateFocusPointPosition(position: [number, number, number]) {
    let focusIndex = 0;
    const endIndex = this.geometry.attributes.position.count;
    while (focusIndex < endIndex) {
      this.geometry.attributes.position.setXYZ(
        focusIndex,
        position[0],
        position[1],
        position[2]
      );
      focusIndex += 2;
    }
    this.geometry.attributes.position.needsUpdate = true;
  }
  /**
   * 更新透明度
   */
  updateOpacity(opacity: number) {
    // (this.material as LineBasicMaterial).opacity = opacity;
    (this.material as ShaderMaterial).uniforms.uOpacity.value = opacity;
  }

  /**
   * 线条流光动画
   */
  update() {
    if (this.enableAnimation) {
      (this.material as ShaderMaterial).uniforms.uProgress.value += this.speed;
      if (
        (this.material as ShaderMaterial).uniforms.uProgress.value > 2 &&
        !isRandom.value
      ) {
        (this.material as ShaderMaterial).uniforms.uProgress.value = -1.5;
      }
    }
  }

  /**
   * 聚焦节点添加连线
   * @param newFocusChildren 增加聚焦节点的直接前驱节点
   */
  addFocusChildren(newFocusChildren: Point[]) {
    const focusPosition = [this.posit[0], this.posit[1], this.posit[2]];
    const focusColor = [this.colors[0], this.colors[1], this.colors[2]];
    newFocusChildren.forEach((point, index) => {
      const randomNum = Number(Math.random().toFixed(2));

      this.posit.push(...focusPosition); //起始点（聚焦节点）
      this.colors.push(...focusColor); //起始点颜色
      this.segmentProgress.push(0);

      this.posit.push(...focusPosition); //结束点
      this.colors.push(...pointStatusToColor(point.status)); //结束点颜色
      point.endPointsIndex = (this.indexs.length + index) * 2 + 1;
      this.segmentProgress.push(1);
      this.indexs.push(this.indexs.length + index);
      this.random.push(randomNum);
    });
    // 传入顶点着色
    this.geometry.setAttribute(
      "position",
      new Float32BufferAttribute(this.posit, 3)
    );
    this.geometry.setAttribute(
      "color",
      new Float32BufferAttribute(this.colors, 3)
    );
    this.geometry.setAttribute(
      "lineIndex",
      new Float32BufferAttribute(this.indexs, 1)
    );
    this.geometry.setAttribute(
      "segmentProgress",
      new Float32BufferAttribute(this.segmentProgress, 1)
    );
    this.geometry.setAttribute(
      "random",
      new Float32BufferAttribute(this.random, 1)
    );
    this.geometry.attributes.position.needsUpdate = true;
    this.geometry.attributes.color.needsUpdate = true;
    this.geometry.attributes.lineIndex.needsUpdate = true;
    this.geometry.attributes.segmentProgress.needsUpdate = true;
    this.geometry.attributes.random.needsUpdate = true;
  }
}
