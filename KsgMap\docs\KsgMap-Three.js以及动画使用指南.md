
## 概述

KsgMap 是一个基于 Three.js 构建的知识图谱可视化库，专门用于渲染大规模的3D知识节点网络。该库采用了现代化的架构设计，结合了高性能的GPU渲染技术和丰富的动画效果。

## 核心架构

### 1. 分层架构设计

KsgMap 采用了清晰的分层架构：

- **配置层 (Config Layer)**: 统一管理所有Three.js组件的配置
- **核心渲染层 (Core Rendering)**: 处理WebGL和CSS2D渲染
- **数据处理层 (Data Processing)**: 负责图数据的布局计算
- **渲染对象层 (Render Objects)**: 具体的3D对象实现
- **动画系统 (Animation System)**: 统一的动画管理

### 2. 主要技术特点

- **高性能渲染**: 使用BufferGeometry和自定义着色器实现GPU加速
- **双渲染器架构**: WebGL渲染器处理3D图形，CSS2D渲染器处理文本标签
- **自定义控制器**: 基于OrbitControls扩展的专用相机控制
- **流光动画**: 通过GLSL着色器实现的连线流光效果
- **动画池管理**: 基于Tween.js的统一动画系统

## 基础配置系统

### 配置文件结构

配置系统位于 `src/components/ksgMap/config/` 目录，主要包含：

```typescript
// 主配置入口
export function useInitThreeJsConfig(option: Options = {}) {
  // 合并用户配置与默认配置
  // 返回各组件的配置对象
}
```

### 核心配置项

#### 1. 相机配置 (CameraConfig)

```typescript
type CameraConfig = {
  fov?: number;        // 视野角度，默认45度
  aspect?: number;     // 宽高比，默认1200/675
  near?: number;       // 近裁剪面，默认0.1
  far?: number;        // 远裁剪面，默认1000
  position?: {         // 相机初始位置
    x: number;         // X轴位置
    y: number;         // Y轴位置  
    z: number;         // Z轴位置
  };
  target?: {           // 相机朝向目标
    x: number;
    y: number;
    z: number;
  };
};
```

#### 2. 渲染器配置 (RendererConfig)

```typescript
type RendererConfig = {
  width: number;       // 渲染宽度
  height: number;      // 渲染高度
  webGLRenderer?: {
    antialias: boolean; // 抗锯齿开关
  };
  css2DRenderer?: {
    domElement: HTMLElement; // CSS2D渲染容器
  };
};
```

#### 3. 场景配置 (SceneConfig)

```typescript
type SceneConfig = {
  backgroundIntensity: number;  // 场景亮度，默认0.02
  backgroundBlurriness: number; // 场景模糊度，默认0.0
  groupPosition: [number, number, number]; // 整体位置偏移
};
```

#### 4. 控制器配置 (ControlsConfig)

```typescript
type ControlsConfig = {
  position: [number, number, number];    // 初始相机位置
  target: [number, number, number];      // 初始目标位置
  minPolarAngle: number;                 // 最小极角
  maxPolarAngle: number;                 // 最大极角
  minDistance: number;                   // 最小缩放距离
  maxDistance: number;                   // 最大缩放距离
  mouseButtons: {                        // 鼠标按键映射
    LEFT: MOUSE;
    MIDDLE: MOUSE;
    RIGHT: MOUSE;
  };
  enableDamping: boolean;                // 阻尼效果
  yMinRange: number;                     // Y轴最小范围
  yMaxRange: number;                     // Y轴最大范围
  yDelta: number;                        // Y轴偏移量
};
```

### 默认配置示例

```typescript
const defaultConfig = {
  camera: {
    fov: 45,
    aspect: 1200 / 675,
    near: 0.1,
    far: 1000,
    position: { x: 30.2, y: -3.14, z: 24.98 }
  },
  renderer: {
    width: 1200,
    height: 675,
    webGLRenderer: { antialias: true }
  },
  scene: {
    backgroundIntensity: 0.02,
    backgroundBlurriness: 0.0,
    groupPosition: [0, 6, 0]
  },
  controls: {
    position: [30, -3.14, 24],
    target: [0, 0, 0],
    minPolarAngle: 0.78539,
    maxPolarAngle: 2.35619,
    minDistance: 1,
    maxDistance: 5000,
    enableDamping: true
  },
  levelSpace: 15,      // 层级间距
  pointSpace: 7,       // 节点间距
  point: {
    radius: 0.5,       // 节点半径
    space: 2           // 节点间隔
  },
  line: {
    length: 0.4,       // 流光长度
    speed: 0.15,       // 流光速度
    isRandom: false    // 是否随机流光
  }
};
```

## 核心组件详解

### 1. KsgPoint - 节点渲染器

KsgPoint 继承自 Three.js 的 Points 类，专门用于高性能渲染大量知识节点。

#### 技术实现原理

```typescript
export default class KsgPoint extends Points {
  // 使用BufferGeometry存储顶点数据
  constructor(points: Point[], total: number, opacity: number, size: number) {
    const pGeo = new BufferGeometry();
    
    // 创建顶点属性缓冲区
    const positionAttribute = new BufferAttribute(
      new Float32Array(total * 3), 3  // 位置属性 (x,y,z)
    );
    const colorAttribute = new BufferAttribute(
      new Float32Array(total * 3), 3  // 颜色属性 (r,g,b)
    );
    const opacityAttribute = new BufferAttribute(
      new Float32Array(total), 1      // 透明度属性
    );
    
    // 使用自定义着色器材质
    const material = new ShaderMaterial({
      uniforms: {
        map: { value: starTexture },
        uTime: { value: Math.random() * 2 * Math.PI },
        isBreathAni: { value: false }
      },
      transparent: true,
      vertexShader: vertShader,
      fragmentShader: fragShader,
      blending: AdditiveBlending  // 加法混合创建发光效果
    });
  }
}
```

#### 核心功能

- **批量渲染**: 使用GPU并行处理大量节点
- **动态属性**: 支持实时更新颜色、透明度、大小
- **特效支持**: 呼吸动画、悬停高亮、聚焦效果
- **高效查找**: ID到索引的映射表，O(1)时间复杂度查找

### 2. KsgLine - 连线渲染器

KsgLine 继承自 LineSegments，实现节点间连接线的渲染和流光动画。

#### 流光动画原理

```typescript
export default class KsgLine2 extends LineSegments {
  constructor(focusPoint: Point, focusChildren: Point[], opacity: number) {
    // 创建线段几何体
    const geo = new BufferGeometry();
    
    // 设置顶点属性
    geo.setAttribute("position", new Float32BufferAttribute(positions, 3));
    geo.setAttribute("segmentProgress", new Float32BufferAttribute(segmentProgress, 1));
    
    // 流光着色器材质
    const material = new ShaderMaterial({
      uniforms: {
        uTime: { value: 0 },        // 动画时间
        uWidth: { value: 0.4 },     // 流光宽度
        uSpeed: { value: 0.15 },    // 流光速度
        uOpacity: { value: opacity }, // 基础透明度
        uIsRandom: { value: true }   // 随机流光
      },
      vertexShader,
      fragmentShader
    });
  }
  
  // 流光动画更新
  update() {
    const material = this.material as ShaderMaterial;
    material.uniforms.uTime.value += this.speed;
  }
}
```

### 3. KsgControls - 相机控制器

基于 OrbitControls 扩展的自定义控制器，专门为知识图谱场景优化。

#### 主要特性

- **范围限制**: 限制相机的移动范围和角度
- **阻尼效果**: 平滑的交互体验
- **自动旋转**: 支持场景自动旋转展示
- **事件处理**: 完整的鼠标、键盘、触摸事件支持

```typescript
export class KsgControls {
  constructor(
    object: PerspectiveCamera,    // 要控制的相机
    rootArea: Group,              // 知识图谱根容器
    domElement: HTMLElement       // 事件监听元素
  ) {
    // 初始化控制器参数
    this.minPolarAngle = 0.78539;   // 最小极角
    this.maxPolarAngle = 2.35619;   // 最大极角
    this.enableDamping = true;      // 启用阻尼
    this.yMinRange = -1000;         // Y轴最小范围
    this.yMaxRange = 110;           // Y轴最大范围
  }
}
```

## 动画系统详解

### 动画库架构

动画系统位于 `src/components/ksgMap/animation/` 目录，基于 Tween.js 构建。

#### 核心动画类型

1. **节点动画** (`load.ts`)
   - `pointEnterAnimation`: 节点进入动画
   - `pointLeaveAnimation`: 节点离开动画
   - `pointHaloEnterAnimation`: 聚焦光圈动画

2. **连线动画** (`load.ts`)
   - `lineEnterAnimation`: 连线进入动画
   - `lineLeaveAnimation`: 连线离开动画

3. **相机动画** (`enterFocus.ts`, `enterGlobal.ts`)
   - `viewMoveAnimation`: 视角切换动画
   - `enterGlobalAnimation`: 进入全局视图动画

4. **标签动画** (`label.ts`)
   - `labelEnter`: 标签显示动画

### 动画实现原理

#### 1. 节点进入动画

```typescript
export function pointEnterAnimation(
  point: Point,
  pointsMesh: KsgPoint,
  option: Option,
  duration: number = 300
) {
  const { x, y, z } = option;
  
  return new Promise((resolve) => {
    new Tween({ opacity: 0, x: x, y: y - 7, z: z, scale: 0 })
      .to({ ...option, scale: 1 }, duration)
      .easing(TWEEN.Easing.Sinusoidal.Out)
      .onUpdate(({ x, y, z, opacity, scale }) => {
        // 直接更新BufferAttribute
        pointsMesh.updateOpacity([point.index!], opacity);
        pointsMesh.updatePosition([point.index!], [x, y, z]);
      })
      .onComplete(() => resolve(point))
      .start();
  });
}
```

#### 2. 相机动画

```typescript
export function viewMoveAnimation(
  controls: KsgControls,
  to: [number, number, number],
  duration: number = 500
) {
  return new Promise((resolve) => {
    new Tween({
      x: controls.target.x,
      y: controls.target.y,
      z: controls.target.z,
      xp: controls.object.position.x,
      yp: controls.object.position.y,
      zp: controls.object.position.z
    })
    .to({
      x: to[0], y: to[1] - 5, z: to[2],
      xp: cx, yp: cy - 12, zp: cz
    }, duration)
    .easing(Easing.Quadratic.InOut)
    .onUpdate(({ x, y, z, xp, yp, zp }) => {
      controls.target.set(x, y, z);
      controls.object.position.set(xp, yp, zp);
    })
    .start();
  });
}
```

### 动画管理器

```typescript
export class KsgAnimation {
  private tweenPool: Tween<any>[] = [];
  
  // 统一的动画更新循环
  update(time: number) {
    for (let i = this.tweenPool.length - 1; i >= 0; i--) {
      const tween = this.tweenPool[i];
      if (!tween.update(time)) {
        this.tweenPool.splice(i, 1); // 动画完成后移除
      }
    }
  }
}
```

## 性能优化策略

### 1. GPU加速渲染

- **BufferGeometry**: 直接在GPU内存中存储顶点数据
- **自定义着色器**: 避免CPU-GPU数据传输开销
- **实例化渲染**: 批量处理相同几何体

### 2. 内存管理

- **对象池**: 重用Tween对象减少GC压力
- **按需加载**: 只渲染可见区域的节点
- **及时释放**: dispose方法清理GPU资源

### 3. 渲染优化

- **视锥体剔除**: 自动剔除视野外的对象
- **LOD系统**: 根据距离调整渲染质量
- **批量更新**: 合并多个属性更新操作

## 使用示例

### 基础初始化

```typescript
import { useInitThreeJsConfig } from './config';

// 初始化配置
const config = useInitThreeJsConfig({
  renderer: {
    width: 1200,
    height: 675
  },
  camera: {
    position: { x: 30, y: -3, z: 25 }
  }
});

// 创建场景
const scene = new Scene();
const camera = new PerspectiveCamera();
const renderer = new WebGLRenderer();
```

### 创建知识节点

```typescript
// 准备节点数据
const pointsData = [
  { id: '1', name: '节点1', coordinate: [0, 0, 0] },
  { id: '2', name: '节点2', coordinate: [5, 0, 0] }
];

// 创建节点渲染器
const ksgPoint = new KsgPoint(pointsData, pointsData.length, 1, 10);
scene.add(ksgPoint);

// 创建连线
const ksgLine = new KsgLine(pointsData[0], [pointsData[1]], 0.8);
scene.add(ksgLine);
```

### 添加动画

```typescript
// 节点进入动画
pointEnterAnimation(
  pointsData[0],
  ksgPoint,
  { opacity: 1, x: 0, y: 0, z: 0 },
  300
).then(() => {
  console.log('动画完成');
});

// 相机动画
viewMoveAnimation(controls, [10, 5, 10], 500);
```

## 着色器系统详解

### GLSL着色器实现

KsgMap 使用自定义GLSL着色器实现高性能渲染和特效。

#### 节点顶点着色器 (pointVert.glsl)

```glsl
// 顶点属性
attribute vec3 customColor;      // 自定义颜色
attribute float customOpacity;   // 自定义透明度
attribute float size;            // 节点大小
attribute float customRandom;    // 随机值
attribute float breathStatus;    // 呼吸状态

// Uniform变量
uniform float uTime;             // 动画时间
uniform bool isBreathAni;        // 是否启用呼吸动画

// 传递给片段着色器的变量
varying vec3 vColor;
varying float vOpacity;
varying float vRandom;

void main() {
  vColor = customColor;
  vOpacity = customOpacity;
  vRandom = customRandom;

  // 计算呼吸动画效果
  float breathScale = 1.0;
  if (isBreathAni && breathStatus > 0.5) {
    breathScale = 1.0 + 0.2 * sin(uTime + vRandom * 6.28);
  }

  // 设置点大小
  gl_PointSize = size * breathScale;

  // 变换顶点位置
  gl_Position = projectionMatrix * modelViewMatrix * vec4(position, 1.0);
}
```

#### 节点片段着色器 (pointFrag.glsl)

```glsl
uniform sampler2D map;           // 纹理贴图
uniform vec2 offset;             // 纹理偏移

varying vec3 vColor;
varying float vOpacity;
varying float vRandom;

void main() {
  // 采样纹理
  vec2 uv = gl_PointCoord + offset;
  vec4 texColor = texture2D(map, uv);

  // 应用颜色和透明度
  gl_FragColor = vec4(vColor * texColor.rgb, texColor.a * vOpacity);

  // 添加发光效果
  float glow = 1.0 - length(gl_PointCoord - vec2(0.5));
  gl_FragColor.rgb += vColor * glow * 0.3;
}
```

#### 连线流光着色器

```glsl
// 顶点着色器
attribute float segmentProgress;  // 线段进度 (0-1)
attribute float random;           // 随机值
varying float vProgress;
varying float vRandom;

void main() {
  vProgress = segmentProgress;
  vRandom = random;
  gl_Position = projectionMatrix * modelViewMatrix * vec4(position, 1.0);
}

// 片段着色器
uniform float uTime;              // 动画时间
uniform float uWidth;             // 流光宽度
uniform float uSpeed;             // 流光速度
uniform float uOpacity;           // 基础透明度
uniform bool uIsRandom;           // 是否随机

varying float vProgress;
varying float vRandom;

void main() {
  // 计算流光位置
  float time = uIsRandom ? (uTime + vRandom * 10.0) : uTime;
  float lightPos = mod(time * uSpeed, 1.0);

  // 计算当前片段到流光中心的距离
  float dist = abs(vProgress - lightPos);

  // 创建流光效果
  float light = 1.0 - smoothstep(0.0, uWidth, dist);
  float alpha = uOpacity + light * (1.0 - uOpacity);

  gl_FragColor = vec4(1.0, 1.0, 1.0, alpha);
}
```

## 数据流处理

### KsgGraph - 图数据处理器

KsgGraph 负责将原始数据转换为可渲染的3D布局。

#### 布局算法

```typescript
export default class KsgGraph {
  // 层级布局计算
  computeLevel(points: Point[]) {
    const queue: Point[] = [];
    const visited = new Set<string>();

    // BFS遍历计算层级
    this.rootPoints.forEach(root => {
      root.level = 0;
      queue.push(root);
      visited.add(root.id);
    });

    while (queue.length > 0) {
      const current = queue.shift()!;

      current.childIds.forEach(childId => {
        const child = this.getPointById(childId);
        if (child && !visited.has(childId)) {
          child.level = current.level + 1;
          queue.push(child);
          visited.add(childId);
        }
      });
    }
  }

  // 3D坐标计算
  computePointPosition() {
    this.levelPointsMap.forEach((points, level) => {
      const radius = level * ctx.levelSpace!;
      const angleStep = (2 * Math.PI) / points.length;

      points.forEach((point, index) => {
        const angle = index * angleStep;
        point.coordinate = [
          radius * Math.cos(angle),
          level * -ctx.levelSpace!,
          radius * Math.sin(angle)
        ];
      });
    });
  }
}
```

### 任务调度器

为了避免大量计算阻塞UI线程，KsgMap 使用任务调度器：

```typescript
class FrameScheduler {
  private tasks: (() => boolean)[] = [];
  private isRunning = false;

  addTask(task: () => boolean) {
    this.tasks.push(task);
    if (!this.isRunning) {
      this.start();
    }
  }

  private start() {
    this.isRunning = true;
    const process = () => {
      const startTime = performance.now();

      // 每帧最多执行16ms的任务
      while (this.tasks.length > 0 && performance.now() - startTime < 16) {
        const task = this.tasks.shift()!;
        const shouldContinue = task();
        if (shouldContinue) {
          this.tasks.push(task);
        }
      }

      if (this.tasks.length > 0) {
        requestAnimationFrame(process);
      } else {
        this.isRunning = false;
      }
    };

    requestAnimationFrame(process);
  }
}
```

## 交互系统

### 事件处理机制

KsgMap 实现了完整的交互事件系统：

```typescript
// 鼠标事件处理
class InteractionManager {
  private raycaster = new Raycaster();
  private mouse = new Vector2();

  onMouseMove(event: MouseEvent) {
    // 更新鼠标坐标
    this.mouse.x = (event.clientX / window.innerWidth) * 2 - 1;
    this.mouse.y = -(event.clientY / window.innerHeight) * 2 + 1;

    // 射线检测
    this.raycaster.setFromCamera(this.mouse, ctx.camera!);
    const intersects = this.raycaster.intersectObject(ctx.pointsMesh!);

    if (intersects.length > 0) {
      const point = this.getPointFromIntersection(intersects[0]);
      this.handleHover(point);
    }
  }

  onMouseClick(event: MouseEvent) {
    const intersects = this.raycaster.intersectObject(ctx.pointsMesh!);
    if (intersects.length > 0) {
      const point = this.getPointFromIntersection(intersects[0]);
      this.handleClick(point);
    }
  }
}
```

### 标签系统

CSS2D标签系统提供了HTML标签的3D定位：

```typescript
export class KsgLabel extends CSS2DObject {
  display(point: Point, option?: ViewRangeOption) {
    // 创建HTML元素
    this.element.innerHTML = `
      <div class='css2d-label-inner'>
        ${reFormateTitle(point.name)}
      </div>
    `;

    // 设置3D位置
    this.position.set(...point.coordinate);

    // 处理MathJax渲染
    renderMathJax(this.element);

    // 计算屏幕位置避免溢出
    if (option) {
      this.setPosition(option);
    }

    this.visible = true;
  }

  private setPosition(option: ViewRangeOption) {
    const { viewRange, dnc } = option;

    // 计算标签在屏幕上的位置
    const screenPos = this.getScreenPosition();

    // 边界检测和调整
    if (screenPos.x + this.labelWidth > viewRange.maxX) {
      this.element.style.transform = 'translateX(-100%)';
    }
    if (screenPos.y + this.labelHeight > viewRange.maxY) {
      this.element.style.transform += ' translateY(-100%)';
    }
  }
}
```

## 高级特性

### 1. 多层级渲染

支持按层级分批加载和渲染：

```typescript
class LevelManager {
  private currentLevel = 0;
  private maxLevel = 4;

  loadLevel(level: number) {
    const points = ctx.graph!.getLevelPoints(level);

    // 批量添加节点
    points.forEach((point, index) => {
      setTimeout(() => {
        this.addPointWithAnimation(point);
      }, index * 50); // 错开动画时间
    });
  }

  private addPointWithAnimation(point: Point) {
    pointEnterAnimation(point, ctx.pointsMesh!, {
      opacity: 1,
      x: point.coordinate[0],
      y: point.coordinate[1],
      z: point.coordinate[2]
    }, 300);
  }
}
```

### 2. 视图模式切换

支持全局视图和聚焦视图的切换：

```typescript
enum VIEW_MODE {
  GLOBAL_VIEW = 'global',
  FOCUS_VIEW = 'focus'
}

class ViewManager {
  switchToGlobal() {
    // 清除所有动画
    TWEEN.removeAll();

    // 切换视图模式
    ctx.viewMode = VIEW_MODE.GLOBAL_VIEW;

    // 相机动画到全局位置
    enterGlobalAnimation(ctx.controls!, [0, 50, 100]);

    // 显示所有节点
    ctx.pointsMesh!.showAllPoints();
  }

  switchToFocus(point: Point) {
    ctx.viewMode = VIEW_MODE.FOCUS_VIEW;

    // 相机动画到聚焦位置
    viewMoveAnimation(ctx.controls!, point.coordinate, 500);

    // 高亮相关节点
    this.highlightRelatedPoints(point);
  }
}
```

### 3. 性能监控

内置性能监控系统：

```typescript
class PerformanceMonitor {
  private frameCount = 0;
  private lastTime = 0;
  private fps = 0;

  update() {
    this.frameCount++;
    const currentTime = performance.now();

    if (currentTime - this.lastTime >= 1000) {
      this.fps = this.frameCount;
      this.frameCount = 0;
      this.lastTime = currentTime;

      // 根据FPS调整渲染质量
      this.adjustQuality();
    }
  }

  private adjustQuality() {
    if (this.fps < 30) {
      // 降低渲染质量
      ctx.renderer!.setPixelRatio(Math.min(window.devicePixelRatio * 0.8, 1));
      ctx.pointsMesh!.setLOD(2); // 降低细节级别
    } else if (this.fps > 50) {
      // 提高渲染质量
      ctx.renderer!.setPixelRatio(window.devicePixelRatio);
      ctx.pointsMesh!.setLOD(0); // 最高细节级别
    }
  }
}
```

## 最佳实践

### 1. 内存管理

```typescript
// 正确的资源释放
class ResourceManager {
  dispose() {
    // 释放几何体
    ctx.pointsMesh?.geometry.dispose();
    ctx.focusLine?.geometry.dispose();

    // 释放材质
    if (ctx.pointsMesh?.material instanceof ShaderMaterial) {
      ctx.pointsMesh.material.dispose();
    }

    // 释放纹理
    starTexture.dispose();

    // 清除动画
    TWEEN.removeAll();

    // 移除事件监听
    ctx.controls?.dispose();
  }
}
```

### 2. 错误处理

```typescript
class ErrorHandler {
  static handleShaderError(error: string) {
    console.error('Shader compilation error:', error);
    // 回退到基础材质
    return new MeshBasicMaterial({ color: 0xff0000 });
  }

  static handleDataError(data: any) {
    if (!data || !Array.isArray(data.dataList)) {
      throw new Error('Invalid data format');
    }

    // 数据验证和清理
    return data.dataList.filter(item =>
      item.id && item.name && item.coordinate
    );
  }
}
```

### 3. 配置优化

```typescript
// 根据设备性能调整配置
function getOptimalConfig(): Options {
  const isMobile = /Android|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent);
  const isLowEnd = navigator.hardwareConcurrency < 4;

  return {
    renderer: {
      width: isMobile ? 800 : 1200,
      height: isMobile ? 600 : 675,
      webGLRenderer: {
        antialias: !isLowEnd
      }
    },
    point: {
      radius: isMobile ? 0.3 : 0.5,
      space: isMobile ? 1.5 : 2
    },
    line: {
      length: isLowEnd ? 0.2 : 0.4,
      speed: isLowEnd ? 0.1 : 0.15
    }
  };
}
```

## 总结

KsgMap 是一个功能强大的知识图谱可视化库，通过合理的架构设计和性能优化，能够流畅地渲染大规模的3D知识网络。其核心优势包括：

1. **高性能**: GPU加速渲染，支持大规模数据
2. **丰富动画**: 基于Tween.js的完整动画系统
3. **灵活配置**: 模块化的配置系统
4. **扩展性强**: 清晰的分层架构便于扩展
5. **自定义着色器**: 实现复杂的视觉效果
6. **智能优化**: 自适应性能调整
7. **完整交互**: 支持多种交互模式

通过理解其底层原理和使用方式，开发者可以快速构建出专业级的知识图谱可视化应用。建议在实际使用中根据具体需求调整配置参数，并注意内存管理和性能优化。
