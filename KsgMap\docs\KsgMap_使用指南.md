# KsgMap 知识图谱可视化组件使用指南

## 快速开始

### 安装依赖

```bash
npm install three @tweenjs/tween.js lodash
npm install -D @types/three
```

### 基本使用

```vue
<template>
  <div class="container">
    <KsgMap
      :width="1200"
      :height="675"
      :config="config"
      :loading="loading"
      @loadMore="handleLoadMore"
      @clickLabel="handleClickLabel"
      ref="ksgMapRef"
    />
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import { KsgMap, MODE } from './components/ksgMap'

const ksgMapRef = ref()
const loading = ref('loading')

// 配置选项
const config = {
  model: MODE.Single_ROOT,  // 单根节点模式
  pointsLevelPager: {
    current: 1,
    levelSize: 2
  }
}

// 加载数据
onMounted(async () => {
  try {
    const data = await fetchKnowledgeData()
    await ksgMapRef.value.firstLoadPointsData(data.dataList, data.total)
    loading.value = 'loaded'
  } catch (error) {
    loading.value = 'error'
  }
})

// 加载更多数据
const handleLoadMore = async (rootId, current, levelSize) => {
  loading.value = 'loading'
  try {
    const data = await fetchMoreData(rootId, current, levelSize)
    await ksgMapRef.value.loadMorePointsData(data.dataList)
    loading.value = 'loaded'
  } catch (error) {
    loading.value = 'error'
  }
}

// 点击标签事件
const handleClickLabel = (nodeId) => {
  console.log('点击了节点:', nodeId)
  // 处理节点点击逻辑
}
</script>
```

## 配置选项详解

### 基础配置

```typescript
interface Options {
  // 显示模式
  model?: MODE.Single_ROOT | MODE.MULTIPLE_ROOT
  
  // 相机配置
  camera?: {
    fov?: number          // 视野角度，默认 45
    aspect?: number       // 宽高比，自动计算
    near?: number         // 近裁剪面，默认 0.1
    far?: number          // 远裁剪面，默认 1000
    position?: {          // 相机初始位置
      x: number
      y: number
      z: number
    }
    target?: {            // 相机朝向目标
      x: number
      y: number
      z: number
    }
  }
  
  // 渲染器配置
  renderer?: {
    width: number         // 画布宽度
    height: number        // 画布高度
    webGLRenderer?: {
      antialias: boolean  // 抗锯齿，默认 true
    }
  }
  
  // 场景配置
  scene?: {
    backgroundIntensity: number    // 背景强度，默认 0.02
    backgroundBlurriness: number   // 背景模糊度，默认 0.0
    groupPosition: [number, number, number]  // 图谱位置
  }
  
  // 控制器配置
  controls?: {
    position: [number, number, number]       // 相机位置
    target: [number, number, number]         // 目标位置
    minPolarAngle: number                    // 最小极角
    maxPolarAngle: number                    // 最大极角
    minDistance: number                      // 最小距离
    maxDistance: number                      // 最大距离
    enableDamping: boolean                   // 启用阻尼
    mouseButtons: {                          // 鼠标按键映射
      LEFT: MOUSE
      MIDDLE: MOUSE
      RIGHT: MOUSE
    }
    yMinRange: number                        // Y轴最小范围
    yMaxRange: number                        // Y轴最大范围
    yDelta: number                           // Y轴增量
  }
}
```

### 高级配置

```typescript
// 节点配置
point?: {
  radius: number        // 节点半径
  space: number         // 节点间距
}

// 连线配置
line?: {
  length?: number       // 流光长度，默认 0.3
  speed?: number        // 流光速度，默认 0.015
  isRandom?: boolean    // 随机流光，默认 true
}

// 悬停标签配置
hoverLabel?: {
  offsetX: number       // X轴偏移
  offsetY: number       // Y轴偏移
}

// 分页配置
pointsLevelPager?: {
  current: number       // 当前页
  levelSize: number     // 每页层数
  total?: number        // 总数
}

// 层级间距
levelSpace?: number     // 默认 20
```

## 数据格式

### 节点数据格式

```typescript
interface PointData {
  pointId: string                    // 节点唯一ID
  pointName: string                  // 节点名称
  parentPointIds: string[]           // 父节点ID数组
  status: POINT_STATUS              // 节点状态
  pointType?: string                // 节点类型
  description?: string              // 节点描述
  metadata?: any                    // 扩展元数据
}

// 节点状态枚举
enum POINT_STATUS {
  LEARNED = 'learned',              // 已学习
  LEARNING = 'learning',            // 学习中
  NOT_LEARNED = 'not_learned'       // 未学习
}
```

### 数据示例

```typescript
const sampleData = [
  {
    pointId: 'root-1',
    pointName: '数学基础',
    parentPointIds: [],
    status: POINT_STATUS.LEARNED
  },
  {
    pointId: 'node-1',
    pointName: '线性代数',
    parentPointIds: ['root-1'],
    status: POINT_STATUS.LEARNING
  },
  {
    pointId: 'node-2',
    pointName: '微积分',
    parentPointIds: ['root-1'],
    status: POINT_STATUS.NOT_LEARNED
  },
  {
    pointId: 'node-3',
    pointName: '矩阵运算',
    parentPointIds: ['node-1'],
    status: POINT_STATUS.NOT_LEARNED
  }
]
```

## API 方法

### 组件方法

```typescript
// 首次加载数据
await ksgMapRef.value.firstLoadPointsData(
  pointsData: PointData[],
  total: number,
  rootId?: string
)

// 加载更多数据
await ksgMapRef.value.loadMorePointsData(
  pointsData: PointData[]
)

// 注入 MathJax（用于数学公式渲染）
await ksgMapRef.value.injectMathJax()
```

### 事件回调

```typescript
// 加载更多数据回调
@loadMore="(rootId: string, current: number, levelSize: number) => void"

// 点击标签回调
@clickLabel="(nodeId: string) => void"
```

## 交互操作

### 鼠标操作

- **左键拖拽**：旋转视角
- **右键拖拽**：平移视角
- **滚轮**：缩放视角
- **鼠标悬停**：显示节点信息
- **单击节点**：聚焦到该节点
- **双击空白**：切换全局视角

### 键盘操作

- **方向键**：移动相机
- **Esc**：退出当前操作

### 控制按钮

- **全屏按钮**：切换全屏显示
- **回退按钮**：返回上一个聚焦节点
- **根节点按钮**：回到根节点视角

## 样式定制

### CSS 变量

```css
.ksg-three-container {
  --primary-color: #1976d2;
  --secondary-color: #424242;
  --success-color: #4caf50;
  --warning-color: #ff9800;
  --error-color: #f44336;
}
```

### 自定义样式

```css
/* 容器样式 */
.ksg-three-container {
  position: relative;
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

/* 控制按钮样式 */
.btn-container {
  position: absolute;
  top: 16px;
  right: 16px;
  z-index: 100;
}

/* 加载状态样式 */
.loading-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 16px;
}
```

