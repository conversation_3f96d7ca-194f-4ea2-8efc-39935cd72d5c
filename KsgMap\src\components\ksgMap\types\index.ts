import type { MOUSE } from "three";
import { POINT_STATUS } from "../enums";
// 相机配置类型
export type CameraConfig = {
  fov?: number;
  aspect?: number;
  near?: number;
  far?: number;
  /*相机位置*/
  position?: {
    x: number;
    y: number;
    z: number;
  };
  /*相机朝向*/
  target?: {
    x: number;
    y: number;
    z: number;
  };
};
// 渲染器配置
export type RendererConfig = {
  /** 宽度 */
  width: number;
  /** 高度 */
  height: number;
  /** webGLRenderer 配置 */
  webGLRenderer?: {
    /** 抗锯齿 */
    antialias: boolean;
  };
  /** css2DRenderer 配置 */
  css2DRenderer?: {
    domElement: HTMLElement;
  };
};
// 配置信息
export type Config = {
  /* 相机配置*/
  camera?: CameraConfig;
  /*渲染器配置*/
  renderer?: RendererConfig;
  /*层级间隔距离*/
  levelSpace?: number;
  /*点之间的间距*/
  pointSpace?: number;
};

/**
 *场景模式
 *singleRoot--单个根节点场景
 *multiplyRoots--多个根节点场景
 */
export type ModelPops = "signalRoot" | "multiplyRoots";

/**
 * 领域类型数据
 */
export type AreasData = {
  areaId: string;
  areaName: string;
  points: AreasData[];
};

/**
 *每个知识点的结构
 */
export type PointData = {
  pointId: string;
  pointName: string;
  parentPointIds: string[];
  isMilestone: boolean;
  /**学习进度 */
  status: number;
};
/**
 * 知识点类型数据
 */
export type PointsData = {
  areaId: string;
  pointId: string;
  pointName: string;
  children: PointsData[];
};

/** 点集 */
export type Points = { [id: string]: Point };
/** 点 */
export interface Point {
  /** 编号 */
  id: string;
  /** 名称 */
  name: string;
  /** 父节点 id */
  parentIds: string[];
  /** 子节点 id */
  childIds: string[];
  /** 层级 */
  level: number;
  /** 坐标 */
  coordinate: [number, number, number];
  /**里程碑 */
  isMilestone: boolean;
  /**学习进度 */
  status: number;
  /**渲染后设置的索引等同于id */
  index?: number;
  /**渲染线后后末节点的索引 */
  endPointsIndex?: number;
}
export interface newPoint extends Point {
  /*节点状态 */
  status: POINT_STATUS;
}
/** 边 */
export interface Edge {
  start: Point;
  end: Point;
}
/** 边集 */
export type Edges = { [key: string]: Edge };
/**
 * 知识点图
 */
export class Graph {
  levels: Points[] = [];
  idLevelMap: { [id: string]: number } = {};
  edges: Edges = {};
  bfsLevel: { [id: string]: number } = {};
  private pointsData: PointData[];
  private maxLevel: number;
  private fullGraph: {
    [id: string]: {
      name: string;
      childIds: string[];
      isMilestone: boolean;
      status: number;
    };
  } = {};
  private levelSpace: number;
  private pointSpace: number;
  constructor(
    pointsData: PointData[],
    maxLevel: number = Infinity,
    levelSpace: number = 6,
    pointSpace: number = 2
  ) {
    this.pointsData = pointsData; //暂存数组存储方式的pointData
    this.maxLevel = maxLevel; // 最大层级
    this.levelSpace = levelSpace; //层级间距

    this.pointSpace = pointSpace; //知识点间距
    this.update({ pointsData, maxLevel });
    console.log(this);
  }

  addPointData(pointsData: PointData[]) {
    this.pointsData.push(...pointsData);
    this.update({ pointsData: this.pointsData });
  }

  update(args?: { pointsData?: PointData[]; maxLevel?: number }) {
    if (!args) return;
    if (args.pointsData) this.build(args.pointsData); //把pointsData维护到fullGraph中
    if (args.maxLevel) this.maxLevel = args.maxLevel;
    // console.log("维护后的数据",this.fullGraph)
    this.idLevelMap = {};
    this.edges = {};
    this.bfsLevel = {};

    const queue: string[] = [];
    const childIds: { [id: string]: string[] } = {};
    const degrees: { [id: string]: number } = {};
    const points: Points = {};

    // 计算每个节点的孩子节点的等级，如果存在一个节点的孩子节点中degrees则加1，并且把保存在pointIdMap中
    for (const id in this.fullGraph) {
      const p = this.fullGraph[id];
      for (const childId of p.childIds) {
        if (!degrees[childId]) degrees[childId] = 0;
        degrees[childId]++;
      }
    }

    // 遍历fullGraph,把等级为0的节点放入queue队列中，同时把该id维护到bfsMap中并设置他为0层级
    for (const id in this.fullGraph) {
      const p = this.fullGraph[id];
      if (!degrees[id]) {
        queue.push(id);
        const point = (points[id] = {
          id,
          name: p.name,
          parentIds: [], //多了一个保存父节点的数组
          childIds: [], //多了一个保存孩子节点的数组
          level: 0,
          coordinate: [0, 0, 0],
          status: p.status,
          isMilestone: p.isMilestone,
        }); //维护到points对象中属性id对应point属性
        this.bfsLevel[id] = 0; //bfsMap记录第一个根节点层级为0
      }
    }
    let level = -1; //初始化层级为-1
    while (queue.length) {
      const id = queue.shift()!;
      const p = this.fullGraph[id];
      const point = points[id];
      level = Math.max(level, point.level); //初始化id和当前队列弹出的id等级和当前层级比较取最大值
      if (point.level + 1 >= this.maxLevel) continue; //如果大于最大层级则跳过
      for (const childId of p.childIds) {
        degrees[childId]--;
        if (!this.bfsLevel[childId])
          this.bfsLevel[childId] = this.bfsLevel[id] + 1; //计算孩子节点的层层级并维护到bfsMap中,(前面已经对根节点进行了运算也得出了结果)
        if (!points[childId]) {
          const q = this.fullGraph[childId];
          points[childId] = {
            id: childId,
            name: q.name,
            parentIds: [],
            childIds: [],
            level: point.level + 1,
            coordinate: [0, 0, 0],
            isMilestone: q.isMilestone,
            status: q.status,
          };
        }
        const childPoint = points[childId];
        childPoint.parentIds.push(id);
        point.childIds.push(childId);
        childPoint.level = Math.max(childPoint.level, point.level + 1);
        if (degrees[childId] === 0) queue.push(childId);
      }
    }

    this.levels = new Array(level + 1).fill(null).map(() => ({})); //数组对象，用来维护层级
    for (const id in points) {
      const point = points[id];
      this.idLevelMap[id] = point.level;
      this.levels[point.level][id] = point; //维护到一个二维数组对象里面
    }
    // 根据遍历points中的child和parent来确定边，并维护到edges中
    for (const id in points) {
      const point = points[id];
      for (const parentId of point.parentIds) {
        this.edges[`${parentId}-${id}`] = {
          start: this.levels[this.idLevelMap[parentId]][parentId],
          end: point,
        };
      }
    }
    for (let i = 0; i < this.levels.length; ++i) {
      this.planLevel(this.levels[i], i);
    }
  }

  private build(pointsData: PointData[]) {
    this.pointsData = pointsData;
    this.fullGraph = {};
    for (const pointData of pointsData) {
      const { pointId, pointName, isMilestone, status, parentPointIds } =
        pointData;
      //创建一个对象类型的Map,属性为pointId，值时一个对象{name: pointName, childIds: []}
      if (!this.fullGraph[pointId])
        this.fullGraph[pointId] = {
          name: pointName,
          childIds: [],
          isMilestone,
          status,
        };
    }

    // 遍历每个节点的父节点集合，并把该节点的pointId添加到childIds集合中
    for (const pointData of pointsData) {
      const { pointId, parentPointIds } = pointData;
      for (const parentId of parentPointIds) {
        this.fullGraph[parentId].childIds.push(pointId);
      }
    }
  }

  private planLevel(points: Points, level: number) {
    let y = -this.levelSpace * level;
    const keys = Object.keys(points); //id对应pointsMap
    const count = keys.length;
    if (count === 1) {
      const id = keys[0];
      points[id].coordinate = [0, y, 0];
    } else {
      const interval = 3;
      const coordinates: [number, number, number][] = [];
      for (let round = 1, s = count; s != 0; ++round) {
        let num = interval * round;
        if (num < s) {
          s -= num;
        } else {
          num = s;
          s = 0;
        }
        //重新计算y值
        y += Math.sin(round);
        const r = round * this.pointSpace;
        for (let i = 0; i < num; ++i) {
          const x = r * Math.cos((2 * Math.PI * i) / num);
          const z = r * Math.sin((2 * Math.PI * i) / num);
          coordinates.push([x, y, z]);
        }
      }
      let i = 0;
      for (const id in points) {
        points[id].coordinate = coordinates[i++];
      }
    }
  }

  /**根据id获取point */
  getPointById(id: string): Point | null {
    return this.levels[this.idLevelMap[id]]?.[id] ?? null;
  }
  /**获取当前图所有节点总数 */
  getPointsLength(): number {
    return this.pointsData.length;
  }

  /**获取当前图所有节点 */
  getPointsData(): Point[] {
    const points: Point[] = [];
    for (const key in this.levels) {
      for (const pointKey in this.levels[key]) {
        points.push(this.levels[key][pointKey]);
      }
    }
    return points;
  }
}

//渲染几何图形的类型
export enum GraphType {
  Point,
  Line,
}

/**子图切换数据 */
export type FocusData = {
  focusPoint: Point;
  rootPointId: string;
  points: Point[];
};

export type EventsCallback = {
  loadMoreCallback?: (
    rootId: string,
    current: number,
    levelSize: number
  ) => void;
  clickLabelCallback?: (id: string) => void;
};

export type Data = {
  dataList: PointData[];
  total: number;
};
export type Response<T> = {
  code: string;
  data: T;
  message: string;
  success: boolean;
};

export type getSignalRootApi<T = Data> = (
  current: number,
  limit: number,
  klgCode: string
) => Promise<Response<T>>;

export type SceneConfig = {
  /**场景l亮度 */
  backgroundIntensity: number;
  /**场景模糊度 */
  backgroundBlurriness: number;
  /**溯源图整体位置 */
  groupPosition: [number, number, number];
};

export type ControlsConfig = {
  position: [number, number, number];
  target: [number, number, number];
  minPolarAngle: number;
  maxPolarAngle: number;
  minDistance: number;
  maxDistance: number;
  mouseButtons: {
    LEFT: MOUSE;
    MIDDLE: MOUSE;
    RIGHT: MOUSE;
  };
  enableDamping: boolean;
  /**沿y轴移动范围 */
  yMinRange: number;
  yMaxRange: number;
  yDelta: number;
};

/**容器尺寸 */
export type Size = {
  width: number;
  height: number;
};

/**
 * 视口坐标范围
 */
export type ViewRange = {
  minX: number;
  maxX: number;
  minY: number;
  maxY: number;
};
