# KsgMap 技术实现原理详解

## 🏗️ 整体架构设计

KsgMap采用分层架构设计，将复杂的3D知识图谱可视化功能分解为多个独立且协作的模块：

### 架构层次

1. **表现层 (Presentation Layer)**
   - Vue组件封装
   - 用户交互界面
   - 事件处理

2. **业务逻辑层 (Business Logic Layer)**
   - 数据处理算法
   - 图结构计算
   - 动画控制

3. **渲染引擎层 (Rendering Engine Layer)**
   - Three.js场景管理
   - WebGL渲染
   - CSS2D标签渲染

4. **数据访问层 (Data Access Layer)**
   - 数据加载
   - 缓存管理
   - API接口

## 🧮 核心算法实现

### 1. DAG图构建算法

```typescript
/**
 * 构建有向无环图(DAG)
 * 时间复杂度: O(V + E)，其中V是节点数，E是边数
 */
private build(pointsData: PointData[]) {
  // 1. 初始化节点Map
  for (const pointData of pointsData) {
    const point: Point = {
      id: pointData.id,
      name: pointData.pointName,
      parentIds: pointData.parentIds || [],
      childIds: [],
      level: 0,
      coordinate: [0, 0, 0],
      isMilestone: pointData.isMilestone,
      status: pointData.status
    }
    this.pointsData.set(pointData.id, point)
  }

  // 2. 构建父子关系
  for (const [id, point] of this.pointsData) {
    for (const parentId of point.parentIds) {
      const parentPoint = this.pointsData.get(parentId)
      if (parentPoint && !parentPoint.childIds.includes(id)) {
        parentPoint.childIds.push(id)
      }
    }
  }
}
```

### 2. 层级计算算法

使用广度优先搜索(BFS)算法计算节点层级：

```typescript
/**
 * BFS层级计算
 * 时间复杂度: O(V + E)
 */
computeLevel(points: Map<string, Point>) {
  const visited = new Set<string>()
  const queue: string[] = []
  
  // 1. 找到所有根节点（没有父节点的节点）
  for (const [id, point] of points) {
    if (point.parentIds.length === 0) {
      queue.push(id)
      point.level = 0
      visited.add(id)
    }
  }

  // 2. BFS遍历计算层级
  while (queue.length > 0) {
    const currentId = queue.shift()!
    const currentPoint = points.get(currentId)!
    
    // 初始化层级数组
    if (!this.idLevelMap[currentPoint.level]) {
      this.idLevelMap[currentPoint.level] = []
    }
    this.idLevelMap[currentPoint.level].push(currentId)

    // 处理子节点
    for (const childId of currentPoint.childIds) {
      const childPoint = points.get(childId)
      if (childPoint && !visited.has(childId)) {
        childPoint.level = currentPoint.level + 1
        queue.push(childId)
        visited.add(childId)
      }
    }
  }
}
```

### 3. 3D坐标计算算法

```typescript
/**
 * 计算节点3D坐标
 * 采用圆形布局算法，避免节点重叠
 */
computePointPosition(levelHeight = 15, pointSpace = 7) {
  for (const level in this.idLevelMap) {
    const levelNum = parseInt(level)
    const pointIds = this.idLevelMap[levelNum]
    const pointCount = pointIds.length
    
    if (pointCount === 1) {
      // 单个节点居中
      const point = this.pointsData.get(pointIds[0])!
      point.coordinate = [0, -levelNum * levelHeight, 0]
    } else {
      // 多个节点圆形分布
      const radius = (pointCount * pointSpace) / (2 * Math.PI)
      const angleStep = (2 * Math.PI) / pointCount
      
      pointIds.forEach((id, index) => {
        const point = this.pointsData.get(id)!
        const angle = index * angleStep
        const x = radius * Math.cos(angle)
        const z = radius * Math.sin(angle)
        const y = -levelNum * levelHeight
        
        point.coordinate = [x, y, z]
      })
    }
  }
}
```

## 🎨 渲染系统实现

### 1. Three.js场景初始化

```typescript
// 场景配置
function useScene(config: SceneConfig) {
  const scene = new Scene()
  
  // 环境贴图设置
  const texLoader = new TextureLoader()
  const texture = texLoader.load(backgroundImage)
  texture.mapping = EquirectangularReflectionMapping
  
  scene.environment = texture
  scene.background = texture
  scene.backgroundIntensity = config.backgroundIntensity
  scene.backgroundBlurriness = config.backgroundBlurriness
  
  // 创建图谱容器组
  const group = new Group()
  group.position.set(...config.groupPosition)
  scene.add(group)
  
  return { scene }
}

// 相机配置
function useCamera(config: CameraConfig) {
  const camera = new PerspectiveCamera(
    config.fov,
    config.aspect,
    config.near,
    config.far
  )
  camera.position.set(
    config.position.x,
    config.position.y,
    config.position.z
  )
  
  return { camera }
}

// 渲染器配置
function useRenderer(config: RendererConfig) {
  const renderer = new WebGLRenderer({
    antialias: config.webGLRenderer.antialias
  })
  renderer.setSize(config.width, config.height)
  renderer.setPixelRatio(window.devicePixelRatio)
  
  return { rendererDom: renderer.domElement }
}
```

### 2. 节点渲染实现

```typescript
/**
 * 创建3D节点
 */
function createPointMesh(point: Point): Mesh {
  // 几何体
  const geometry = new SphereGeometry(
    ctx.point.radius,
    32,
    32
  )
  
  // 材质（根据节点状态选择不同材质）
  let material: Material
  if (point.isMilestone) {
    material = new MeshStandardMaterial({
      color: 0xffd700, // 金色里程碑
      metalness: 0.8,
      roughness: 0.2
    })
  } else if (point.status === 1) {
    material = new MeshStandardMaterial({
      color: 0x4caf50, // 绿色已学习
      metalness: 0.3,
      roughness: 0.7
    })
  } else {
    material = new MeshStandardMaterial({
      color: 0x2196f3, // 蓝色未学习
      metalness: 0.3,
      roughness: 0.7
    })
  }
  
  const mesh = new Mesh(geometry, material)
  mesh.position.set(...point.coordinate)
  mesh.userData = { pointId: point.id, type: 'point' }
  
  return mesh
}
```

### 3. 连线流光效果实现

```typescript
/**
 * 创建流光连线
 */
function createFlowLine(startPoint: Point, endPoint: Point): Group {
  const group = new Group()
  
  // 1. 创建连线管道
  const curve = new CatmullRomCurve3([
    new Vector3(...startPoint.coordinate),
    new Vector3(...endPoint.coordinate)
  ])
  
  const tubeGeometry = new TubeGeometry(curve, 64, 0.05, 8, false)
  const tubeMaterial = new MeshBasicMaterial({
    color: 0x666666,
    transparent: true,
    opacity: 0.6
  })
  const tubeMesh = new Mesh(tubeGeometry, tubeMaterial)
  group.add(tubeMesh)
  
  // 2. 创建流光效果
  const flowGeometry = new TubeGeometry(curve, 64, 0.08, 8, false)
  const flowMaterial = new MeshBasicMaterial({
    color: 0x00ffff,
    transparent: true,
    opacity: 0
  })
  const flowMesh = new Mesh(flowGeometry, flowMaterial)
  group.add(flowMesh)
  
  // 3. 流光动画
  const flowLength = ctx.line.length || 0.4
  const flowSpeed = ctx.line.speed || 0.15
  
  function animateFlow() {
    const time = Date.now() * 0.001 * flowSpeed
    const progress = (time % 1)
    
    // 计算流光位置和透明度
    const startProgress = Math.max(0, progress - flowLength)
    const endProgress = Math.min(1, progress)
    
    if (endProgress > startProgress) {
      flowMaterial.opacity = 0.8
      // 更新流光几何体显示范围
      updateFlowGeometry(flowGeometry, curve, startProgress, endProgress)
    } else {
      flowMaterial.opacity = 0
    }
    
    requestAnimationFrame(animateFlow)
  }
  
  animateFlow()
  return group
}
```

## 🎮 交互系统实现

### 1. 射线检测

```typescript
/**
 * 鼠标射线检测
 */
function handleMouseEvent(event: MouseEvent) {
  const rect = container.getBoundingClientRect()
  const mouse = new Vector2(
    ((event.clientX - rect.left) / rect.width) * 2 - 1,
    -((event.clientY - rect.top) / rect.height) * 2 + 1
  )
  
  const raycaster = new Raycaster()
  raycaster.setFromCamera(mouse, ctx.camera)
  
  // 检测与节点的交集
  const intersects = raycaster.intersectObjects(
    ctx.viewGroup.children,
    true
  )
  
  if (intersects.length > 0) {
    const intersect = intersects[0]
    const userData = intersect.object.userData
    
    if (userData.type === 'point') {
      handlePointInteraction(userData.pointId, event.type)
    }
  }
}
```

### 2. 相机动画控制

```typescript
/**
 * 平滑相机过渡动画
 */
function animateCameraTo(targetPosition: Vector3, targetLookAt: Vector3) {
  const startPosition = ctx.camera.position.clone()
  const startLookAt = ctx.controls.target.clone()
  
  const tween = new TWEEN.Tween({
    positionProgress: 0,
    lookAtProgress: 0
  })
  .to({
    positionProgress: 1,
    lookAtProgress: 1
  }, 1000)
  .easing(TWEEN.Easing.Cubic.InOut)
  .onUpdate((object) => {
    // 插值计算相机位置
    ctx.camera.position.lerpVectors(
      startPosition,
      targetPosition,
      object.positionProgress
    )
    
    // 插值计算观察目标
    ctx.controls.target.lerpVectors(
      startLookAt,
      targetLookAt,
      object.lookAtProgress
    )
    
    ctx.controls.update()
  })
  .start()
}
```

## 🔄 数据流管理

### 1. 响应式数据更新

```typescript
/**
 * 数据更新流程
 */
class DataUpdateManager {
  private updateQueue: UpdateTask[] = []
  private isUpdating = false
  
  async addUpdateTask(task: UpdateTask) {
    this.updateQueue.push(task)
    
    if (!this.isUpdating) {
      await this.processUpdateQueue()
    }
  }
  
  private async processUpdateQueue() {
    this.isUpdating = true
    
    while (this.updateQueue.length > 0) {
      const task = this.updateQueue.shift()!
      
      try {
        await this.executeTask(task)
      } catch (error) {
        console.error('数据更新失败:', error)
      }
    }
    
    this.isUpdating = false
  }
  
  private async executeTask(task: UpdateTask) {
    switch (task.type) {
      case 'ADD_NODES':
        await this.addNodes(task.data)
        break
      case 'UPDATE_NODES':
        await this.updateNodes(task.data)
        break
      case 'REMOVE_NODES':
        await this.removeNodes(task.data)
        break
    }
  }
}
```

### 2. 性能优化策略

```typescript
/**
 * 帧调度器 - 避免阻塞主线程
 */
class FrameScheduler {
  private tasks: (() => boolean)[] = []
  private isRunning = false
  
  addTask(task: () => boolean) {
    this.tasks.push(task)
    
    if (!this.isRunning) {
      this.start()
    }
  }
  
  private start() {
    this.isRunning = true
    this.processFrame()
  }
  
  private processFrame() {
    const startTime = performance.now()
    const maxFrameTime = 16 // 16ms保证60fps
    
    while (this.tasks.length > 0 && 
           (performance.now() - startTime) < maxFrameTime) {
      const task = this.tasks.shift()!
      const isCompleted = task()
      
      if (!isCompleted) {
        this.tasks.unshift(task) // 任务未完成，重新加入队列
        break
      }
    }
    
    if (this.tasks.length > 0) {
      requestAnimationFrame(() => this.processFrame())
    } else {
      this.isRunning = false
      this.onCompleted()
    }
  }
  
  onCompleted() {
    // 所有任务完成的回调
  }
}
```

## 🎯 最佳实践

### 1. 内存管理

- 及时清理Three.js对象（geometry、material、texture）
- 使用对象池复用频繁创建的对象
- 监听组件销毁事件，清理事件监听器

### 2. 性能优化

- 使用LOD（Level of Detail）技术处理大量节点
- 实现视锥体裁剪，只渲染可见节点
- 合理使用requestAnimationFrame控制渲染频率

### 3. 用户体验

- 提供加载状态反馈
- 实现平滑的动画过渡
- 支持键盘快捷键操作

这个技术实现原理文档详细解释了KsgMap的核心算法和实现细节，帮助开发者深入理解组件的工作原理。
