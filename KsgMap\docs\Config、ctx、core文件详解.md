# KsgMap 知识图谱组件详细文档

## 概述

KsgMap 是一个基于 Three.js 构建的 3D 知识图谱可视化组件，专门用于展示复杂的知识节点关系。该组件采用分层架构设计，包含配置层、上下文层和核心层，提供了丰富的交互功能和视觉效果。

## 架构设计

### 整体架构
- **配置层 (config/)**：负责 Three.js 各个组件的初始化和配置
- **上下文层 (ctx/)**：全局状态管理，维护组件间共享数据
- **核心层 (core/)**：核心业务逻辑，包括图布局算法、渲染逻辑等

## 目录结构详解

### 1. config/ 配置层

配置层是整个组件的基础设施层，负责初始化和配置 Three.js 的各个核心组件。

#### 1.1 index.ts - 配置管理中心
**作用**：统一管理所有配置项，提供默认配置和配置合并功能

**核心功能**：
- 定义默认配置参数（相机、渲染器、场景、控制器等）
- 提供配置合并函数 `useInitThreeJsConfig()`
- 将配置同步到全局上下文 `ctx`

**关键配置项**：
```typescript
const defaultConfig = {
  model: MODE.Single_ROOT,        // 图谱模式（单根/多根）
  camera: {                       // 相机配置
    fov: 45,                     // 视野角度
    aspect: 1200 / 675,          // 宽高比
    near: 0.1,                   // 近裁剪面
    far: 1000,                   // 远裁剪面
    position: { x: 30.2, y: -3.14, z: 24.98 }
  },
  renderer: {                     // 渲染器配置
    width: 1200,
    height: 675,
    webGLRenderer: { antialias: true }
  },
  scene: {                        // 场景配置
    backgroundIntensity: 0.02,    // 背景亮度
    backgroundBlurriness: 0.0,    // 背景模糊度
    groupPosition: [0, 6, 0]      // 图谱整体位置
  }
}
```

#### 1.2 camera.ts - 相机配置
**作用**：创建和配置 Three.js 透视相机

**Three.js 基础知识**：
- **透视相机 (PerspectiveCamera)**：模拟人眼视觉，具有近大远小的透视效果
- **视野角度 (FOV)**：决定相机的视野范围，45度提供自然视觉效果
- **宽高比 (Aspect)**：必须与画布尺寸匹配，避免拉伸变形
- **裁剪面 (Near/Far)**：定义相机可见范围，超出范围的物体不会渲染

**实现原理**：
```typescript
const camera = new PerspectiveCamera(
  config.fov,      // 视野角度
  config.aspect,   // 宽高比
  config.near,     // 近裁剪面
  config.far       // 远裁剪面
);
camera.position.set(x, y, z);  // 设置相机位置
```

#### 1.3 renderer.ts - WebGL渲染器配置
**作用**：创建和配置 Three.js 的 WebGL 渲染器

**Three.js 基础知识**：
- **WebGLRenderer**：使用 WebGL API 进行硬件加速的 3D 渲染
- **抗锯齿 (Antialias)**：消除锯齿边缘，提升视觉质量
- **设备像素比**：处理高 DPI 显示器，确保清晰显示

**实现原理**：
```typescript
const renderer = new THREE.WebGLRenderer({
  antialias: true  // 开启抗锯齿
});
renderer.setSize(width, height);                    // 设置渲染尺寸
renderer.setPixelRatio(window.devicePixelRatio);    // 设置像素比
```

#### 1.4 css2dRenderer.ts - CSS2D渲染器配置
**作用**：创建 CSS2D 渲染器，用于在 3D 场景中渲染 HTML 元素

**Three.js 基础知识**：
- **CSS2DRenderer**：将 HTML 元素精确定位到 3D 空间中的特定位置
- **自动坐标转换**：将 3D 世界坐标转换为 2D 屏幕坐标
- **深度测试**：支持 HTML 元素的遮挡关系

**应用场景**：
- 知识节点的文本标签显示
- 3D 场景中的 UI 界面元素
- 需要复杂样式的文本内容

**实现原理**：
```typescript
const css2DRenderer = new CSS2DRenderer();
css2DRenderer.setSize(width, height);
css2DRenderer.domElement.style.position = "absolute";
css2DRenderer.domElement.style.top = "0px";
```

#### 1.5 scene.ts - 场景配置
**作用**：创建和配置 Three.js 场景，设置 3D 世界环境

**Three.js 基础知识**：
- **Scene**：3D 世界的容器，包含所有 3D 对象、光源、相机等
- **环境贴图**：影响场景中物体的反射效果和整体光照
- **背景设置**：定义用户看到的场景背景

**实现原理**：
```typescript
const scene = new Scene();
const texture = texLoader.load(bgImg);              // 加载背景图片
texture.mapping = EquirectangularReflectionMapping; // 设置全景映射
scene.environment = texture;                        // 设置环境贴图
scene.background = texture;                         // 设置背景
scene.backgroundIntensity = 0.02;                   // 设置背景亮度

const group = new Group();                          // 创建容器组
group.position.set(...config.groupPosition);       // 设置位置
scene.add(group);                                   // 添加到场景
```

#### 1.6 controls.ts - 控制器配置
**作用**：创建自定义相机控制器，处理用户交互

**核心功能**：
- 鼠标/触摸控制相机的旋转、缩放、平移
- 限制相机的移动范围和角度
- 支持阻尼效果，提供平滑交互体验
- 针对知识图谱场景优化的控制逻辑

**实现原理**：
```typescript
const controls = new KsgControls(
  ctx.camera!,                           // 要控制的相机
  ctx.viewGroup!,                        // 知识图谱根容器
  ctx.css2dRenderer?.domElement!         // 事件监听元素
);
```

**重要说明**：使用 CSS2D 渲染器的 DOM 元素作为事件监听目标，因为它在 HTML 层级中位置更高，能正确接收鼠标事件。

#### 1.7 event.ts - 事件系统
**作用**：知识图谱交互事件的核心管理器

**主要功能**：
- 节点悬停效果和标签显示
- 节点点击进入聚焦模式
- 双击进入全局视角
- 相机控制器事件管理
- 数据懒加载触发
- 视角切换和历史记录管理

**事件处理流程**：
1. 绑定鼠标事件到容器元素
2. 使用射线检测确定鼠标指向的 3D 对象
3. 根据交互类型触发相应的处理逻辑
4. 更新视觉效果和状态

#### 1.8 update.ts - 尺寸更新
**作用**：处理画布尺寸变化时的更新逻辑

**更新内容**：
- WebGL 渲染器尺寸
- CSS2D 渲染器尺寸
- 相机宽高比和投影矩阵
- 视口范围计算

### 2. ctx/ 上下文层

#### 2.1 index.ts - 全局状态管理
**作用**：维护整个组件的全局状态和共享数据

**核心数据结构**：
```typescript
export type Context = {
  // Three.js 核心对象
  scene: Scene;                    // 3D 场景
  viewGroup: Group;               // 知识图谱容器组
  camera: PerspectiveCamera;      // 透视相机
  renderer: WebGLRenderer;        // WebGL 渲染器
  css2dRenderer: CSS2DRenderer;   // CSS2D 渲染器
  controls: KsgControls;          // 相机控制器
  
  // 业务状态
  model: MODE;                    // 图谱模式（单根/多根）
  viewMode: VIEW_MODE;            // 视角模式（聚焦/全局）
  loadStatus: LOAD_STATUS;        // 加载状态
  
  // 数据结构
  graph: KsgGraph;                // 知识图谱数据结构
  pointsMesh: KsgPoint;           // 节点渲染对象
  focusLine: KsgLine;             // 聚焦连线对象
  
  // 交互状态
  focusPointInfo: { pointId: string };  // 当前聚焦节点
  focusStack: string[];                 // 子图历史栈
  isControls: boolean;                  // 是否在控制状态
  
  // 配置参数
  levelSpace: number;             // 层级间隔距离
  pointSpace: number;             // 节点间距
  maxDistance: number;            // 弹窗显示距离限制
  viewRange: ViewRange;           // 视口范围
}
```

**设计原理**：
- 采用单例模式，确保全局状态一致性
- 提供类型安全的状态访问
- 支持模块间的数据共享和通信

### 3. core/ 核心层

核心层包含了知识图谱的核心业务逻辑和算法实现。

#### 3.1 KsgGraph.ts - 图布局算法
**作用**：知识图谱空间布局计算器，核心算法引擎

**主要功能**：
- 解析节点间的父子关系，构建图结构
- 计算节点的层级分布（BFS 层次遍历）
- 计算每个节点在 3D 空间中的精确坐标
- 支持增量数据加载和位置更新
- 提供异步计算能力，避免阻塞 UI 线程

**算法特点**：
- 使用广度优先搜索确定节点层级
- 同层节点均匀分布，避免重叠
- 支持多根节点的复杂图结构
- 优化的增量更新算法，提高性能

**核心数据结构**：
```typescript
class KsgGraph {
  pointsData: Map<string, Point>;           // 节点数据映射表
  idLevelMap: { [level: number]: string[] }; // 层级索引映射
  diffData: DiffData;                       // 增量更新差异数据
}
```

**布局算法流程**：
1. **构建图结构**：解析原始数据，建立父子关系
2. **计算层级**：使用 BFS 算法确定节点层级
3. **计算坐标**：根据层级和位置计算 3D 坐标

#### 3.2 KsgPoints.ts - 节点渲染
**作用**：知识节点的高性能渲染类

**主要功能**：
- 批量渲染大量知识节点（使用 GPU 加速）
- 支持节点的颜色、大小、透明度动态变化
- 实现节点的悬停高亮和聚焦效果
- 支持呼吸动画等特殊视觉效果
- 提供高效的节点查找和状态管理

**技术特点**：
- 使用 BufferGeometry 优化内存使用
- 自定义 GLSL 着色器实现复杂视觉效果
- 加法混合模式创建发光效果
- 支持实例化渲染，性能优异

**核心数据结构**：
```typescript
class KsgPoint extends Points {
  pointsData: Point[];                    // 节点数据数组
  idIndexMap: { [key: string]: number };  // ID到索引映射表
  lastHoverIndex: number;                 // 上一个悬停节点索引
  focusIndex: number;                     // 当前聚焦节点索引
  isBreathAni: boolean;                   // 呼吸动画状态
}
```

#### 3.3 KsgLine.ts - 连线渲染
**作用**：知识节点连线的渲染类，实现流光动画效果

**主要功能**：
- 渲染节点间的连接关系
- 实现流光动画效果，增强视觉表现
- 支持动态颜色和透明度变化
- 高性能的批量线条渲染
- 支持随机和同步的流光模式

**技术特点**：
- 使用 BufferGeometry 优化性能
- 自定义 GLSL 着色器实现流光效果
- 支持顶点颜色插值
- 透明混合模式创建柔和效果

**流光动画原理**：
- 通过着色器 uniform 变量控制流光位置
- 使用 segmentProgress 标识线段上的位置
- 动态调整透明度创建流动效果

#### 3.4 KsgControls.ts - 相机控制
**作用**：基于 OrbitControls 扩展的自定义相机控制器

**主要功能**：
- 鼠标/触摸控制相机的旋转、缩放、平移
- 限制相机的移动范围和角度
- 支持阻尼效果，提供平滑交互体验
- 针对知识图谱场景优化的控制逻辑
- 支持自动旋转功能

**扩展特性**：
- Y 轴移动范围限制
- 知识图谱场景适配
- 控制状态管理
- 自动旋转控制

#### 3.5 KsgHover.ts - 悬停效果
**作用**：实现节点悬停时的视觉反馈效果

**主要功能**：
- 显示悬停节点的光圈效果
- 动态扩散动画
- 颜色状态映射
- 相机朝向自适应

**实现原理**：
- 使用 InstancedMesh 优化性能
- 自定义着色器实现扩散效果
- 实时更新朝向相机的方向

#### 3.6 KsgLabel.ts - 标签渲染
**作用**：实现 3D 场景中的 HTML 标签显示

**主要功能**：
- 在 3D 空间中显示 HTML 标签
- 自动位置计算和边界检测
- 支持 MathJax 数学公式渲染
- 动态显示和隐藏控制

**技术实现**：
- 继承 CSS2DObject 类
- 动态 HTML 内容生成
- 位置自适应算法

## Three.js 基础概念解释

### 1. 场景 (Scene)
场景是 3D 世界的容器，包含所有的 3D 对象、光源、相机等。可以理解为一个虚拟的 3D 空间。

### 2. 相机 (Camera)
相机定义了观察 3D 世界的视角。透视相机模拟人眼视觉，具有近大远小的透视效果。

### 3. 渲染器 (Renderer)
渲染器负责将 3D 场景渲染成 2D 图像显示在屏幕上。WebGL 渲染器使用 GPU 加速。

### 4. 几何体 (Geometry)
几何体定义了 3D 对象的形状，包含顶点、面、UV 坐标等信息。

### 5. 材质 (Material)
材质定义了 3D 对象的外观，包括颜色、纹理、光照响应等属性。

### 6. 网格 (Mesh)
网格是几何体和材质的组合，形成可见的 3D 对象。

## 数据流和渲染流程

### 数据流向图

```mermaid
graph TD
    A[原始数据 PointData[]] --> B[KsgGraph 图布局计算]
    B --> C[计算节点层级 BFS]
    C --> D[计算3D坐标]
    D --> E[KsgPoints 节点渲染]
    D --> F[KsgLine 连线渲染]
    E --> G[WebGL渲染器]
    F --> G
    G --> H[Canvas显示]

    I[用户交互] --> J[事件系统]
    J --> K[射线检测]
    K --> L[更新状态]
    L --> M[重新渲染]
    M --> H
```

### 渲染循环

组件使用 `requestAnimationFrame` 实现渲染循环：

```typescript
function startRenderFrame(time: any = 0) {
  const deltaTime = clock.getDelta();

  // CSS2D标签渲染
  ctx.css2dRenderer?.render(ctx.scene!, ctx.camera!);

  // WebGL场景渲染
  ctx.renderer?.render(ctx.scene!, ctx.camera!);

  // 控制器更新
  ctx.controls?.update(deltaTime);
  ctx.controls?.autoRotateUpdate(deltaTime);

  // 动画更新
  TWEEN.update(time);

  // 继续下一帧
  requestAnimationFrame(startRenderFrame);

  // 特效更新
  KsgHover.update(ctx, deltaTime);
  focusCrust.update(deltaTime);
}
```

## 核心算法详解

### 1. 图布局算法 (KsgGraph)

#### BFS 层级计算
```typescript
computeLevel(points: Map<string, Point>) {
  const queue: string[] = [];
  const visited = new Set<string>();

  // 找到所有根节点（没有父节点的节点）
  for (const [id, point] of points) {
    if (!point.parentIds || point.parentIds.length === 0) {
      point.level = 0;
      queue.push(id);
      visited.add(id);
      this.addToLevelMap(0, id);
    }
  }

  // BFS遍历计算层级
  while (queue.length > 0) {
    const currentId = queue.shift()!;
    const currentPoint = points.get(currentId)!;

    // 处理所有子节点
    if (currentPoint.childrenIds) {
      for (const childId of currentPoint.childrenIds) {
        if (!visited.has(childId)) {
          const childPoint = points.get(childId)!;
          childPoint.level = currentPoint.level + 1;
          queue.push(childId);
          visited.add(childId);
          this.addToLevelMap(childPoint.level, childId);
        }
      }
    }
  }
}
```

#### 3D坐标计算
```typescript
computePointPosition(levelHeight = 15, pointSpace = 7) {
  for (const [level, ids] of Object.entries(this.idLevelMap)) {
    const levelNum = parseInt(level);
    const nodeCount = ids.length;

    // 计算该层的Y坐标
    const y = -levelNum * levelHeight;

    // 计算节点在该层的分布
    ids.forEach((id, index) => {
      const point = this.pointsData.get(id)!;

      if (nodeCount === 1) {
        // 单个节点居中
        point.coordinate = [0, y, 0];
      } else {
        // 多个节点均匀分布
        const totalWidth = (nodeCount - 1) * pointSpace;
        const startX = -totalWidth / 2;
        const x = startX + index * pointSpace;
        point.coordinate = [x, y, 0];
      }
    });
  }
}
```

### 2. 着色器渲染 (Shader)

#### 节点着色器 (Point Shader)
顶点着色器负责计算每个节点的屏幕位置：
```glsl
// 顶点着色器
attribute float size;
attribute vec3 color;
varying vec3 vColor;

void main() {
  vColor = color;
  vec4 mvPosition = modelViewMatrix * vec4(position, 1.0);
  gl_PointSize = size * (300.0 / -mvPosition.z);
  gl_Position = projectionMatrix * mvPosition;
}
```

片段着色器负责渲染每个像素的颜色：
```glsl
// 片段着色器
varying vec3 vColor;

void main() {
  float distance = length(gl_PointCoord - vec2(0.5));
  if (distance > 0.5) discard;

  float alpha = 1.0 - smoothstep(0.3, 0.5, distance);
  gl_FragColor = vec4(vColor, alpha);
}
```

#### 连线流光着色器 (Line Shader)
```glsl
// 流光效果片段着色器
uniform float uTime;
uniform float uLength;
uniform float uSpeed;
varying float vProgress;
varying vec3 vColor;

void main() {
  float flowPosition = mod(uTime * uSpeed, 1.0 + uLength);
  float distance = abs(vProgress - flowPosition);

  float alpha = 1.0 - smoothstep(0.0, uLength, distance);
  alpha *= smoothstep(0.0, 0.1, distance);

  gl_FragColor = vec4(vColor, alpha);
}
```

## 性能优化策略

### 1. 几何体优化
- 使用 `BufferGeometry` 替代 `Geometry`，减少内存占用
- 合并同类几何体，减少绘制调用次数
- 使用实例化渲染处理大量相似对象

### 2. 渲染优化
- 视锥体剔除：只渲染相机视野内的对象
- LOD (Level of Detail)：根据距离调整细节级别
- 对象池：重用几何体和材质对象

### 3. 异步计算
- 使用 `FrameScheduler` 将复杂计算分帧执行
- Web Worker 处理大数据量计算
- 懒加载：按需加载数据

### 4. 内存管理
```typescript
// 正确的资源释放
dispose() {
  this.geometry.dispose();
  this.material.dispose();
  if (this.material.map) this.material.map.dispose();
}
```

## 交互系统详解

### 1. 射线检测 (Raycasting)
```typescript
function getIntersectObjects(event: MouseEvent) {
  const mouse = new Vector2();
  const rect = canvas.getBoundingClientRect();

  // 将鼠标坐标转换为标准化设备坐标
  mouse.x = ((event.clientX - rect.left) / rect.width) * 2 - 1;
  mouse.y = -((event.clientY - rect.top) / rect.height) * 2 + 1;

  // 创建射线
  const raycaster = new Raycaster();
  raycaster.setFromCamera(mouse, camera);

  // 检测相交对象
  const intersects = raycaster.intersectObjects(scene.children, true);
  return intersects;
}
```

### 2. 事件处理流程
1. **鼠标事件捕获**：监听 DOM 事件
2. **坐标转换**：将屏幕坐标转换为 3D 世界坐标
3. **射线检测**：确定鼠标指向的 3D 对象
4. **状态更新**：根据交互类型更新组件状态
5. **视觉反馈**：更新渲染效果

### 3. 动画系统
使用 Tween.js 实现平滑动画：
```typescript
// 相机移动动画
new TWEEN.Tween(camera.position)
  .to({ x: targetX, y: targetY, z: targetZ }, 1000)
  .easing(TWEEN.Easing.Cubic.InOut)
  .onUpdate(() => {
    camera.lookAt(target);
  })
  .start();
```

## 扩展和自定义

### 1. 自定义着色器
```typescript
const customMaterial = new ShaderMaterial({
  uniforms: {
    uTime: { value: 0.0 },
    uColor: { value: new Color(0xff0000) }
  },
  vertexShader: `
    void main() {
      gl_Position = projectionMatrix * modelViewMatrix * vec4(position, 1.0);
    }
  `,
  fragmentShader: `
    uniform float uTime;
    uniform vec3 uColor;
    void main() {
      gl_FragColor = vec4(uColor, 1.0);
    }
  `
});
```

### 2. 自定义控制器
```typescript
class CustomControls extends KsgControls {
  constructor(camera, domElement) {
    super(camera, domElement);
    this.enableCustomFeature = true;
  }

  update(deltaTime) {
    super.update(deltaTime);
    // 添加自定义逻辑
    if (this.enableCustomFeature) {
      this.handleCustomInteraction();
    }
  }
}
```

## 常见问题和解决方案

### 1. 性能问题
**问题**：大量节点时帧率下降
**解决方案**：
- 使用实例化渲染
- 实现视锥体剔除
- 降低远距离对象的细节级别

### 2. 内存泄漏
**问题**：长时间使用后内存持续增长
**解决方案**：
- 正确释放 Three.js 资源
- 移除事件监听器
- 清理定时器和动画

### 3. 移动端适配
**问题**：移动设备上性能差或显示异常
**解决方案**：
- 降低渲染分辨率
- 简化着色器效果
- 优化触摸事件处理

## 总结

KsgMap 组件通过分层架构设计，将复杂的 3D 知识图谱可视化功能分解为清晰的模块：

1. **配置层**提供了 Three.js 组件的标准化配置和初始化
2. **上下文层**实现了全局状态管理和模块间通信
3. **核心层**包含了图布局算法和渲染逻辑的具体实现

这种设计使得组件具有良好的可维护性、可扩展性和性能表现，为复杂的知识图谱可视化提供了强大的技术支撑。通过深入理解 Three.js 的基础概念和组件的实现原理，开发者可以更好地使用和扩展这个组件。
