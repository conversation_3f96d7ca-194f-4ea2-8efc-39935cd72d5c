    varying float vIntensity;
    uniform float uThickness;
    varying vec2 vUv;
    void main() {

        vec4 worldPosition = modelMatrix * vec4(position, 1.0);
        vec3 worldNormal = normalize(modelMatrix * vec4(position, 0.0)).xyz;
        vec3 dirToCamera = normalize(cameraPosition - worldPosition.xyz);
        vIntensity = 1.0 - dot(worldNormal , dirToCamera);
        vIntensity = pow(vIntensity, uThickness);

        gl_Position = projectionMatrix * modelViewMatrix * vec4(position,1.0);

        vUv = uv;
    }



