import { CSS2DObject } from "three/examples/jsm/renderers/CSS2DRenderer.js";
import type { Point, ViewRange } from "../types";
/**
 * 用于计算节点的世界坐标
 */
export declare class KsgLabel extends CSS2DObject {
    labelWidth: number;
    labelHeight: number;
    /**
     *当前悬浮节点
     */
    point: Point | null;
    lastIndex: number | null;
    offset: {
        x: number;
        y: number;
    };
    constructor(offset?: {
        x: number;
        y: number;
    });
    /**
     * label显示
     * @param {Point} point 当前label对应的point数据
     * @param {any} option 设置label出现位置
     */
    display(point: Point, option?: {
        viewRange: ViewRange;
        dnc: {
            x: number;
            y: number;
        };
    }): void;
    private setPosition;
    /**
     * 移出
     */
    hide(): void;
    distanceShow(show: boolean): void;
    /**
     * 坐标更新
     * @param position 坐标
     */
    updatePosition(position: [number, number, number]): void;
}
/**hover标签 */
declare const hoverLabel: KsgLabel;
/**focus标签 */
declare const focusLabel: KsgLabel;
export { hoverLabel, focusLabel };
