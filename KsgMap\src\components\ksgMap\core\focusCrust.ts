import {
  TextureLoader,
  RepeatWrapping,
  InstancedMesh,
  SphereGeometry,
  ShaderMaterial,
  AdditiveBlending,
  Color,
} from "three";
import { studyStatusToColor } from "../utils";
import { Point } from "../types";
import focusCrustImg from "../assets/images/OIP-A.jfif";
import vertexShader from "../shader/vert.glsl?raw";
import fragmentShader from "../shader/frag.glsl?raw";

/**
 * 聚焦装饰外壳模块 - focusCrust.ts
 *
 * 职责：
 * 1. 为当前聚焦节点提供视觉装饰效果
 * 2. 通过动态外壳增强聚焦节点的识别度
 * 3. 支持根据节点状态变化颜色
 * 4. 提供流动纹理动画，增强视觉吸引力
 *
 * 视觉特点：
 * - 球体几何形状包围聚焦节点
 * - 使用着色器实现动态纹理效果
 * - 加法混合模式创建发光效果
 * - 根据节点学习状态显示不同颜色
 */

// 创建纹理加载器并加载外壳纹理
const textureLoader = new TextureLoader();
const focusTreTexture = textureLoader.load(focusCrustImg);
// 设置纹理重复包装模式，支持纹理动画
focusTreTexture.wrapS = focusTreTexture.wrapT = RepeatWrapping;

// 共享的球体几何体，优化内存使用
const haloGeo = new SphereGeometry(1, 30, 15);

/**
 * 聚焦外壳类 - 继承自Three.js的InstancedMesh
 *
 * 使用实例化网格技术，即使只渲染一个外壳也保持架构的一致性
 * 支持未来扩展为多个聚焦点的场景
 */
export class FocusCrust extends InstancedMesh {
  /** 上一个绑定的聚焦节点 - 用于避免重复绑定和状态管理 */
  lastPoint: Point | null = null;

  /**
   * 构造函数 - 初始化聚焦外壳
   *
   * @param size 外壳大小倍数 - 相对于节点的放大系数
   * @param opacity 透明度 - 外壳的整体透明度 (0-1)
   */
  constructor(size: number, opacity: number = 1.0) {
    super(
      haloGeo,
      new ShaderMaterial({
        uniforms: {
          uThickness: {
            value: 1.8, // 外壳厚度系数
          },
          uNoiseTexture: {
            value: focusTreTexture, // 动态纹理贴图
          },
          uColor: {
            value: new Color(0xfff), // 基础颜色，会根据节点状态动态变化
          },
          uTime: {
            value: 0, // 时间统一变量，用于纹理动画
          },
          opacity: {
            value: 1.0, // 整体透明度
          },
        },
        vertexShader,
        fragmentShader,
        transparent: true,
        blending: AdditiveBlending, // 加法混合，创建发光效果
        depthWrite: false, // 不写入深度缓冲，避免遮挡其他透明物体
      }),
      1 // 实例数量为1
    );

    // 设置渲染顺序，确保外壳在节点之后渲染
    this.renderOrder = 2;
  }

  /**
   * 渲染帧更新函数 - 在每一帧中调用
   *
   * 负责推进纹理动画的时间进度
   * 只有在外壳可见时才执行，优化性能
   *
   * @param delta 时间增量 - 两帧之间的时间差
   */
  update(delta: number = 0.01) {
    if (this.visible) {
      // 推进着色器中的时间变量，驱动纹理动画
      (this.material as ShaderMaterial).uniforms.uTime.value += delta;
    }
  }

  /**
   * 显示外壳并绑定到指定节点
   *
   * 执行流程：
   * 1. 设置外壳位置到节点坐标
   * 2. 根据节点状态设置颜色
   * 3. 显示外壳并缓存节点引用
   *
   * @param bindPoint 要绑定的聚焦节点对象
   */
  display(bindPoint: Point) {
    // 设置外壳的3D位置与节点坐标一致
    this.position.set(...bindPoint.coordinate);

    // 根据节点的学习状态设置外壳颜色
    (this.material as ShaderMaterial).uniforms.uColor.value =
      studyStatusToColor(bindPoint.status);

    // 显示外壳
    this.visible = true;

    // 缓存当前绑定的节点
    this.lastPoint = bindPoint;
  }

  /**
   * 隐藏外壳并清理状态
   *
   * 在退出聚焦模式或切换聚焦节点时调用
   */
  hide() {
    this.visible = false;
    this.lastPoint = null;
  }

  /**
   * 更新外壳位置
   *
   * 用于节点位置发生变化时同步外壳位置
   * 通常在动画过程中调用
   *
   * @param position 新的3D坐标 [x, y, z]
   */
  updatePosition(position: [number, number, number]) {
    this.position.set(...position);
  }
}

// 创建全局唯一的聚焦外壳实例
const focusCrust = new FocusCrust(1);
focusCrust.hide(); // 初始状态为隐藏

export default focusCrust;
