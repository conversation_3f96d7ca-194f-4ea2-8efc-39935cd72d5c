import { Points, Color, PerspectiveCamera } from "three";
import { Point } from "../types";
export default class KsgPoint extends Points {
    pointsData: Point[];
    idIndexMap: {
        [key: string]: number;
    };
    lastHoverIndex: number;
    focusIndex: number;
    /**呼吸动画状态 */
    isBreathAni: boolean;
    focusChildrenIndexArr: Set<number>;
    constructor(points: Point[], total: number, opacity?: number, size?: number);
    /**
     * 释放内存资源
     */
    dispose(): void;
    /**
     * 根据索引获取节点数据
     * @param {number} index 索引
     */
    getPointData(index: number): Point | null;
    /**
     * 根据id查询节点数据
     * @param {string} id id
     */
    getPointDataById(id: string): Point;
    /**
     *更新位置
     @param {number[]} indexArr 索引数组-支持批量操作
     @param {[number, number, number]} position 位置
     */
    updatePosition(indexArr: number[], position: [number, number, number]): void;
    /**
     * 更新颜色
     * @param {number[]} indexArr 索引数组-支持批量操作
     * @param {Color} color 颜色
     */
    updateColor(indexArr: number[], color: Color): void;
    /**
     * 更新尺寸
     * @param {number[]} indexArr 索引数组-支持批量操作
     * @param {number} size 尺寸
     */
    updateSize(indexArr: number[], size: number): void;
    /**
     * 更新透明度
     * @param {number[]} indexArr 索引数组-支持批量操作
     * @param {number} opacity 透明度
     */
    updateOpacity(indexArr: number[], opacity: number): void;
    /**
     * 节点呼吸动画
     * @param {boolean} option 开关
     * @default true 开启状态
     */
    breathAnimationSwitch(option?: boolean): void;
    /**
     * 开启或关闭某个节点的呼吸动画
     * @param index 节点索引
     * @param enable 是否开启
     * @default true 开启状态
     */
    enablePointBreath(index: number, enable?: boolean): void;
    /**
     * 动画更新函数
     */
    update(): void;
    /**
     * 触发某个索引下节点的hover状态
     * @param {number} index 索引
     */
    toggleHover(index?: number): void;
    /**
     *触发某个索引聚焦状态
     *@param {number} index 索引
     */
    toggleFocus(index: number): void;
    /**
     * 初始化高亮的节点,下次聚焦其他
     * 节点时可以在没聚焦状态下自动变暗
     * @param {Point[]} points 节点数组（必须携带index）
     */
    setHightLightPoints(points: Point[]): void;
    /**
     *触发直接前驱状态
     *@param {number[]} indexArr 索引
     */
    toggleFocusChildren(indexArr: number[]): void;
    /**
     * 获取某个索引下节点的dnc坐标
     * @param {number} index 索引
     * @param {PerspectiveCamera} camera 相机
     * @param {number} rendererW 渲染宽度
     * @param {number} rendererH 渲染高度
     */
    getWorldP(index: number, camera: PerspectiveCamera, rendererW: number, rendererH: number): {
        x: number;
        y: number;
    };
    /**
     *加载更多,更新节点
     */
    loadMore(newPointsData: Point[], opacity?: number, size?: number): void;
    /**
     * 更新节点信息
     * @param {Point} point 点信息
     */
    updatePointInfo(point: Point): void;
    /**
     *测试方法-获取渲染的节点真实的3d坐标
     */
    getPoint3DPosition(id: string): number[];
}
