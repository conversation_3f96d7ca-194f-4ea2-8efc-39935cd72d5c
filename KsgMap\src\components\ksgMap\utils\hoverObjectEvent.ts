import * as THREE from "three";
import type { Size, Point } from "../types";
import { type Context } from "../ctx";

/**
 * 创建悬停点事件处理函数
 * 用于处理3D场景中点对象的鼠标悬停事件，支持进入和离开回调
 * 使用射线投射检测鼠标位置对应的点对象
 * 
 * @param elSize 容器元素的尺寸信息
 * @param ctx 应用上下文，包含相机、点网格等对象
 * @param enterCallback 鼠标进入点对象时的回调函数
 * @param leaveCallback 鼠标离开点对象时的回调函数
 * @returns 包含事件处理、清理和尺寸更新方法的对象
 */
export default function createHoverPointEventFun(
  elSize: Size,
  ctx: Partial<Context>,
  enterCallback: (data: Point) => void,
  leaveCallback: () => void
) {
  /** 容器宽度，用于坐标转换 */
  let width: number | null = elSize.width;
  /** 容器高度，用于坐标转换 */
  let height: number | null = elSize.height;
  /** 射线投射器，用于检测鼠标位置对应的3D对象 */
  let raycaster: null | THREE.Raycaster = new THREE.Raycaster();

  /**
   * 清空闭包造成的内存泄漏
   * 将所有引用设置为null，释放内存
   */
  function clear() {
    width = null;
    height = null;
    raycaster = null;
  }

  /**
   * 更新容器尺寸
   * 当容器大小改变时需要调用此方法更新坐标转换参数
   * @param w 新的宽度
   * @param h 新的高度
   */
  function updateSize(w: number, h: number) {
    width = w;
    height = h;
  }
  /**
   * 鼠标移动事件处理函数
   * 检测鼠标位置对应的点对象，触发进入或离开回调
   * @param e 鼠标事件对象
   */
  function event(e: MouseEvent) {
    // 检查点网格是否存在，以及控制器是否正在操作
    if (!ctx.pointsMesh || ctx.controls?.isControls) return;
    
    const { offsetX, offsetY } = e;
    
    // 将屏幕坐标转换为标准化设备坐标(-1到1)
    raycaster!.setFromCamera(
      new THREE.Vector2(
        (offsetX / width!) * 2 - 1,
        -(offsetY / height!) * 2 + 1
      ),
      ctx.camera!
    );
    
    // 检测与点网格的交集
    const intersects = raycaster!.intersectObject(ctx.pointsMesh);
    let pointIndex: number | null = null;
    
    if (intersects.length > 0) {
      pointIndex = intersects[0].index as number; // 获取点的索引
    }
    
    // 如果有相交对象且距离在允许范围内，触发进入回调
    if (intersects[0]?.object && intersects[0].distance <= ctx.maxDistance!) {
      const id = ctx.pointsMesh.getPointData(pointIndex!)?.id;
      enterCallback(ctx.graph?.getPointById(id!)!);
    } else {
      // 否则触发离开回调
      leaveCallback();
    }
  }

  // 返回公开的方法接口
  return {
    clear,        // 清理方法
    event,        // 事件处理方法
    updateSize,   // 尺寸更新方法
  };
}
