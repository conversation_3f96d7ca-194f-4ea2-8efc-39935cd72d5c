# 全局视角自动标签显示功能

## 功能概述

在全局视角下，当相机自动旋转时，系统会智能地显示当前视野范围内的节点标签。这个功能通过多重检测算法，确保只有在合适位置和距离的节点才会显示标签，避免界面过于拥挤。

## 核心特性

### 1. 智能检测算法
- **视锥体检测** - 只显示相机视野内的节点
- **距离检测** - 控制标签显示的最大距离
- **深度检测** - 优先显示前景区域的节点
- **角度检测** - 确保节点在合理的视角范围内

### 2. 性能优化
- **帧率控制** - 每N帧检测一次，避免过度计算
- **动态管理** - 智能显示/隐藏标签，减少DOM操作
- **内存管理** - 及时清理不需要的标签实例

### 3. 用户体验
- **平滑过渡** - 标签显示/隐藏带有动画效果
- **自动适配** - 根据相机位置自动调整显示策略
- **无干扰** - 不影响用户的其他交互操作

## 实现原理

### 检测流程

```mermaid
graph TD
    A[相机旋转] --> B[每N帧触发检测]
    B --> C[更新视锥体]
    C --> D[遍历所有节点]
    D --> E{视锥体检测}
    E -->|通过| F{距离检测}
    E -->|不通过| G[跳过节点]
    F -->|通过| H{深度检测}
    F -->|不通过| G
    H -->|通过| I{角度检测}
    H -->|不通过| G
    I -->|通过| J[显示标签]
    I -->|不通过| G
    G --> K{还有节点?}
    J --> K
    K -->|是| D
    K -->|否| L[更新标签状态]
```

### 检测条件详解

#### 1. 视锥体检测
```typescript
// 检查节点是否在相机的视锥体内
if (!this.frustum.containsPoint(pointPosition)) {
  return false;
}
```

#### 2. 动态距离检测
```typescript
// 计算相机到Y轴的距离（水平观察半径）
const cameraToYAxis = Math.sqrt(
  cameraPosition.x * cameraPosition.x +
  cameraPosition.z * cameraPosition.z
);

// 动态计算maxDistance：相机到Y轴距离减去偏移量
const dynamicMaxDistance = Math.max(
  this.config.minDistance,
  cameraToYAxis - this.config.yAxisOffset
);

// 检查节点距离相机是否在动态范围内
const distanceToCamera = pointPosition.distanceTo(cameraPosition);
if (distanceToCamera > dynamicMaxDistance) {
  return false;
}
```

#### 3. 深度检测
```typescript
// 检查节点是否在前景区域
const depth = cameraToPoint.dot(cameraToTarget.normalize());
const targetDepth = cameraToTarget.length();
if (depth > targetDepth + this.config.maxDepth) {
  return false;
}
```

#### 4. 角度检测
```typescript
// 检查节点是否在合理的视角范围内
const angleToPoint = cameraToTarget.angleTo(cameraToPoint);
const maxAngle = (camera.fov * Math.PI / 180) * this.config.viewAngleFactor;
if (angleToPoint > maxAngle) {
  return false;
}
```

## 配置参数

### 默认配置
```typescript
const DEFAULT_CONFIG = {
  yAxisOffset: 15,       // Y轴距离偏移量
  minDistance: 25,       // 最小显示距离
  maxDepth: 50,          // 最大深度范围
  viewAngleFactor: 0.8,  // 视野角度系数
  updateInterval: 3,     // 每3帧检测一次
};
```

### 参数说明

| 参数名 | 类型 | 默认值 | 说明 | 推荐范围 |
|--------|------|--------|------|----------|
| `yAxisOffset` | number | 15 | Y轴距离偏移量，相机到Y轴距离减去此值作为maxDistance | 10-30 |
| `minDistance` | number | 25 | 最小显示距离，避免maxDistance过小 | 20-50 |
| `maxDepth` | number | 50 | 最大深度范围，相对于相机前方的深度限制 | 30-100 |
| `viewAngleFactor` | number | 0.8 | 视野角度系数，控制视锥体检测的严格程度 | 0.5-1.0 |
| `updateInterval` | number | 3 | 更新频率控制，每N帧检测一次 | 1-5 |

## 使用方法

### 1. 基础使用

系统已自动集成，无需手动调用：

```typescript
// 进入全局视图时自动启用
// 在 enterGlobalView.ts 中：
globalLabelManager.enable();

// 退出全局视图时自动禁用
// 在 event.ts 中：
globalLabelManager.disable();
```

### 2. 自定义配置

```typescript
import { globalLabelManager } from '../utils/globalViewLabelManager';

// 调整配置以适应不同场景
globalLabelManager.updateConfig({
  yAxisOffset: 10,      // 减少偏移量，显示更多节点
  minDistance: 30,      // 增加最小距离
  maxDepth: 60,         // 增加深度范围
  viewAngleFactor: 0.9, // 放宽视角限制
  updateInterval: 2,    // 提高检测频率
});
```
- 使用示例和配置说明

```
/**
 * 
 *
 * 1. 基础使用：
 * ```typescript
 * // 启用全局标签管理（通常在进入全局视图时调用）
 * globalLabelManager.enable();
 *
 * // 禁用全局标签管理（通常在退出全局视图时调用）
 * globalLabelManager.disable();
 * ```
 *
 * 2. 自定义配置：
 * ```typescript
 * // 调整显示距离和检测参数
 * globalLabelManager.updateConfig({
 *   maxDistance: 100,      // 增加最大显示距离
 *   maxDepth: 60,         // 增加深度范围
 *   viewAngleFactor: 0.9, // 放宽视角限制
 *   updateInterval: 2,    // 提高检测频率
 * });
 * ```
 *
 * 3. 监控状态：
 * ```typescript
 * // 获取当前显示的标签数量
 * const labelCount = globalLabelManager.getActiveLabelCount();
 * console.log(`当前显示 ${labelCount} 个标签`);
 *
 * // 获取当前配置
 * const config = globalLabelManager.getConfig();
 * console.log('当前配置:', config);
 * ```
 *
 * 4. 性能优化建议：
 * - 在节点数量较多时，适当增加 updateInterval 减少检测频率
 * - 根据场景调整 maxDistance 和 maxDepth 参数
 * - 在不需要时及时调用 disable() 释放资源
 */

```
### 3. 监控和调试

```typescript
// 获取当前显示的标签数量
const labelCount = globalLabelManager.getActiveLabelCount();
console.log(`当前显示 ${labelCount} 个标签`);

// 获取当前配置
const config = globalLabelManager.getConfig();
console.log('当前配置:', config);
```

## 性能优化建议

### 1. 节点数量优化
- **少量节点(< 100)**: 使用默认配置
- **中等数量(100-500)**: 增加 `updateInterval` 到 4-5
- **大量节点(> 500)**: 减少 `maxDistance`，增加 `updateInterval`

### 2. 场景适配
- **密集场景**: 减少 `maxDistance` 和 `viewAngleFactor`
- **稀疏场景**: 增加 `maxDistance` 和 `maxDepth`
- **演示模式**: 使用较大的 `viewAngleFactor` 显示更多标签

### 3. 性能监控
```typescript
// 监控标签数量变化
setInterval(() => {
  const count = globalLabelManager.getActiveLabelCount();
  if (count > 20) {
    console.warn('标签数量过多，考虑调整配置');
  }
}, 5000);
```

