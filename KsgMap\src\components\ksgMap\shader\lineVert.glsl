attribute float lineIndex;
attribute float segmentProgress;
attribute float random;

varying vec3 vColor;
varying float vIndex;
varying vec3 vPosition;
varying float vSegmentProgress;
varying float vCustomRandom;
void main(){
    vColor = color;
    vIndex = lineIndex;
    vPosition = position;
    vSegmentProgress = segmentProgress;
    vCustomRandom = random; 
    // vCustomRandom = vPosition.x; 
    gl_Position = projectionMatrix*modelViewMatrix*vec4(position,1.0);
}