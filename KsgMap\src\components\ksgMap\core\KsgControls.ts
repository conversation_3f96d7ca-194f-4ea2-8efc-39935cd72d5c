/**
 * 知识图谱专用控制器 - KsgControls.ts
 * 
 * 职责：
 * 1. 管理3D场景中的相机控制逻辑
 * 2. 支持多种控制模式（知识点层/领域层）
 * 3. 提供平滑的交互体验（阻尼、限制等）
 * 4. 处理鼠标、触摸、键盘等多种输入方式
 * 5. 集成自动旋转和控制状态检测
 *    
 * 技术特点：
 * - 基于Three.js的球面坐标系统
 * - 支持多点触控操作
 * - 自适应Y轴范围限制
 * - 事件驱动的架构设计
 * - 高性能的实时更新机制
 */

// 导入Three.js核心模块和类型定义
import {
  EventDispatcher,    // 事件分发器基类，用于实现观察者模式
  PerspectiveCamera, // 透视相机，模拟人眼视觉效果
  MOUSE,            // 鼠标按键枚举
  Quaternion,       // 四元数，用于3D旋转计算
  Spherical,        // 球面坐标系，用于相机轨道控制
  TOUCH,            // 触摸手势枚举
  Vector2,          // 二维向量，用于屏幕坐标计算
  Vector3,          // 三维向量，用于3D空间坐标
  type BaseEvent,   // 基础事件类型
  Matrix4,          // 4x4矩阵，用于3D变换
  Group,            // 3D对象组
  Object3D,         // 3D对象基类
  Vector4,          // 四维向量
} from "three";
// 导入工具函数
import { cmap } from "../utils";

/**
 * 控制器模式枚举
 * 定义了不同的相机控制模式，适应不同的使用场景
 */
enum KsgMode {
  /** 知识点层模式 - 标准的节点浏览模式 */
  Star,
  /** 领域层模式 - 领域级别的宏观控制模式 */
  Domain,
}

// 预定义的事件对象，避免频繁创建对象，提高性能
const _startEvent = { type: "start" } as BaseEvent;    // 控制操作开始事件
const _endEvent = { type: "end" } as BaseEvent;        // 控制操作结束事件
const _changeEvent = { type: "change" } as BaseEvent;  // 相机状态变化事件

/**
 * 知识图谱专用控制器类
 * 继承自Three.js的EventDispatcher，支持事件驱动的交互模式
 */
class KsgControls extends EventDispatcher<{ [key: string]: BaseEvent }> {
  // === 核心组件引用 ===
  /** 控制的透视相机对象 */
  object: PerspectiveCamera;
  /** 根领域对象组 - 用于领域层模式的控制 */
  rootAreaObj: Group;
  /** 子领域对象 - 领域模式下的控制目标 */
  subareas: Object3D | null;
  /** DOM元素 - 用于事件监听和交互 */
  domElement: HTMLElement;

  // === 基础控制属性 ===
  /** 控制器总开关 */
  enabled: boolean;
  /** 相机观察目标点 - 相机总是朝向这个点 */
  target: Vector3;
  
  // === 距离控制 ===
  /** 相机距离目标的最小值 - 防止过度靠近 */
  minDistance: number;
  /** 相机距离目标的最大值 - 防止无限远离 */
  maxDistance: number;
  
  // === 角度控制 ===
  /** 极角的最小值 - 限制垂直旋转范围 */
  minPolarAngle: number;
  /** 极角的最大值 - 限制垂直旋转范围 */
  maxPolarAngle: number;
  
  // === 阻尼系统 ===
  /** 是否启用阻尼效果 - 提供平滑的控制体验 */
  enableDamping: boolean;
  /** 阻尼系数 - 控制减速的程度 */
  dampingFactor: number;
  
  // === 功能开关 ===
  /** 是否允许缩放操作 */
  enableZoom: boolean;
  /** 缩放速度倍数 */
  zoomSpeed: number;
  /** 是否允许旋转操作 */
  enableRotate: boolean;
  /** 旋转速度倍数 */
  rotateSpeed: number;
  /** 是否允许平移操作 */
  enablePan: boolean;
  /** 平移速度倍数 */
  panSpeed: number;
  
  // === 模式控制 ===
  /** 当前控制模式 */
  mode: KsgMode;
  
  // === 输入映射 ===
  /** 键盘按键映射 */
  keys: { LEFT: string; UP: string; RIGHT: string; BOTTOM: string };
  /** 鼠标按键功能映射 */
  mouseButtons: { LEFT: MOUSE; MIDDLE: MOUSE; RIGHT: MOUSE };
  /** 触摸手势映射 */
  touches: { ONE: TOUCH; TWO: TOUCH };
  /** 键盘控制的移动速度 */
  keyPanSpeed: number;
  
  // === Y轴范围控制 ===
  /** 相机Y轴的最小范围 - 适应知识图谱的纵向结构 */
  yMinRange: number;
  /** 相机Y轴的最大范围 */
  yMaxRange: number;
  /** 目标点Y轴的范围偏移量 */
  yDelta: number;
  /** Y轴方向向量 */
  yAxis: Vector3;

  // === 自动功能 ===
  /** 是否启用自动旋转 */
  autoRotate: boolean;
  /** 自动旋转的速度 */
  autoRotateSpeed: number;
  
  // === 状态检测 ===
  /** 是否正在进行控制操作 */
  isControls: boolean;
  /** 控制状态检测的计时器 */
  controlsTimer: number | NodeJS.Timeout | null;

  // === 内部状态 ===
  /** 键盘事件监听的DOM元素 */
  _domElementKeyEvents: HTMLElement | null;
  
  // === 方法引用 ===
  /** 更新函数 - 每帧调用以应用控制变化 */
  update: (deltaTime?: number | null) => boolean;
  /** 清理函数 - 移除所有事件监听器 */
  dispose: () => void;
  /** 模式切换函数 - 在不同控制模式间切换 */
  changeMode: (mode: KsgMode) => void;

  /**
   * 构造函数 - 初始化知识图谱控制器
   * 
   * @param object 要控制的透视相机对象
   * @param rootArea 根领域对象组
   * @param domElement 用于事件监听的DOM元素
   */
  constructor(
    object: PerspectiveCamera,
    rootArea: Group,
    domElement: HTMLElement
  ) {
    // 调用父类构造函数，初始化事件分发器
    super();
    
    // 保存核心对象引用
    this.object = object;           // 要控制的相机
    this.rootAreaObj = rootArea;    // 根领域对象组
    this.subareas = null;          // 子领域对象（初始为空）
    this.domElement = domElement;   // DOM事件监听元素
    
    // 禁用DOM元素的默认触摸滚动行为，确保触摸事件被控制器处理
    this.domElement.style.touchAction = "none";

    // 初始化基础控制属性
    this.enabled = true;           // 启用控制器

    // 初始化相机目标和距离限制
    this.target = new Vector3();   // 相机观察目标点（默认原点）
    this.minDistance = 0;          // 最小距离（允许到达目标点）
    this.maxDistance = Infinity;   // 最大距离（无限远）
    
    // 初始化角度限制（弧度制）
    this.minPolarAngle = 0;        // 最小极角（可以看到正上方）
    this.maxPolarAngle = Math.PI;  // 最大极角（可以看到正下方）
    
    // 初始化阻尼系统
    this.enableDamping = false;    // 默认关闭阻尼
    this.dampingFactor = 0.05;     // 阻尼系数（较小的值提供更平滑的减速）
    
    // 初始化功能开关和速度设置
    this.enableZoom = true;        // 启用缩放功能
    this.zoomSpeed = 0.4;          // 缩放速度（较小的值提供更精确的控制）
    this.enableRotate = true;      // 启用旋转功能
    this.rotateSpeed = 1.0;        // 旋转速度（标准速度）
    this.enablePan = true;         // 启用平移功能
    this.panSpeed = 1.0;           // 平移速度（标准速度）
    
    // 设置默认控制模式
    this.mode = KsgMode.Star;      // 默认为知识点层模式
    
    // 配置键盘按键映射（使用箭头键）
    this.keys = {
      LEFT: "ArrowLeft",           // 左箭头键
      UP: "ArrowUp",               // 上箭头键
      RIGHT: "ArrowRight",         // 右箭头键
      BOTTOM: "ArrowDown",         // 下箭头键
    };
    
    // 配置鼠标按键功能映射
    this.mouseButtons = {
      LEFT: MOUSE.ROTATE,          // 左键用于旋转
      MIDDLE: MOUSE.DOLLY,         // 中键用于缩放
      RIGHT: MOUSE.PAN,            // 右键用于平移
    };
    
    // 配置触摸手势映射
    this.touches = { 
      ONE: TOUCH.ROTATE,           // 单指触摸用于旋转
      TWO: TOUCH.DOLLY_PAN         // 双指触摸用于缩放和平移
    };
    
    // 设置键盘控制速度
    this.keyPanSpeed = 7.0;        // 键盘平移速度
    
    // 配置Y轴范围控制（适应知识图谱的垂直结构）
    this.yMinRange = -100;         // Y轴最小范围
    this.yMaxRange = 100;          // Y轴最大范围
    this.yDelta = 100;             // Y轴偏移量
    this.yAxis = new Vector3(0, 0, 0); // Y轴方向向量
    
    // 初始化键盘事件监听元素引用
    this._domElementKeyEvents = null;

    // 初始化自动旋转功能
    this.autoRotate = false;       // 默认关闭自动旋转
    this.autoRotateSpeed = 2.0;    // 自动旋转速度
    
    // 初始化控制状态检测
    this.isControls = false;       // 当前未在控制中
    this.controlsTimer = null;     // 控制状态检测计时器

    // 定义核心的更新方法，使用闭包模式避免频繁创建临时变量
    this.update = (function () {
      // 创建局部变量，避免每次调用时重复创建对象
      const offset = new Vector3();       // 相机位置相对于目标的偏移向量

      // 创建四元数用于坐标系转换（使Y轴向上作为轨道轴）
      const quat = new Quaternion().setFromUnitVectors(
        object.up,               // 相机的上方向
        new Vector3(0, 1, 0)    // 世界坐标系的Y轴向上
      );
      const quatInverse = quat.clone().invert(); // 逆四元数，用于反向转换
      
      // 缓存上一帧的状态，用于检测变化
      const lastPosition = new Vector3();      // 上一帧相机位置
      const lastQuaternion = new Quaternion(); // 上一帧相机旋转
      const lastTargetPosition = new Vector3(); // 上一帧目标位置
      const twoPI = 2 * Math.PI;               // 2π常量

      /**
       * 实际的更新函数 - 每帧调用以应用所有控制变化
       * @param deltaTime 可选的时间增量，用于基于时间的动画
       * @returns boolean 如果相机状态发生变化返回true
       */
      return function update(deltaTime: number | null = null) {
        // 获取当前相机位置
        const position = scope.object.position;
        // 计算相机相对于目标的偏移向量
        offset.copy(position).sub(scope.target);

        // 将偏移向量转换到"Y轴向上"的坐标空间
        offset.applyQuaternion(quat);

        // 将笛卡尔坐标转换为球面坐标（从Z轴绕Y轴的角度）
        spherical.setFromVector3(offset);
        
        // 根据当前控制模式应用不同的更新逻辑
        switch (scope.mode) {
          case KsgMode.Star:
            if (scope.enableDamping) {
              spherical.theta += sphericalDelta.theta * scope.dampingFactor;
              spherical.phi += sphericalDelta.phi * scope.dampingFactor;
            } else {
              spherical.theta += sphericalDelta.theta;
              spherical.phi += sphericalDelta.phi;
            }
            break;
          case KsgMode.Domain:
            break;
        }

        // restrict phi to be between desired limits
        spherical.phi = Math.max(
          scope.minPolarAngle,
          Math.min(scope.maxPolarAngle, spherical.phi)
        );
        spherical.makeSafe();

        // move target to panned location
        switch (scope.mode) {
          case KsgMode.Star:
            panOffset.setX(0);
            panOffset.setZ(0);
            if (scope.enableDamping === true) {
              scope.target.addScaledVector(panOffset, scope.dampingFactor);
            } else {
              scope.target.add(panOffset);
            }
            // 限制 target 在 y 轴上的范围
            scope.target.y = Math.max(scope.target.y, scope.yMinRange);
            scope.target.y = Math.min(
              scope.target.y,
              scope.yMaxRange - scope.yDelta
            );
            break;
          case KsgMode.Domain:
            if (scope.enableDamping === true) {
              scope.rootAreaObj.updateWorldMatrix(true, false);
              //@ts-ignore
              const offset = new Vector4(...panOffset, 0)
                .multiplyScalar(scope.dampingFactor)
                .applyMatrix4(scope.rootAreaObj.matrixWorld.clone().invert());
              scope.subareas?.position.add(offset);
            } else {
              scope.rootAreaObj.updateWorldMatrix(true, false);
              //@ts-ignore

              const offset = new Vector4(...panOffset, 0).applyMatrix4(
                scope.rootAreaObj.matrixWorld.clone().invert()
              );
              scope.subareas?.position.add(offset);
            }
            break;
        }

        let zoomChanged = false;
        const prevRadius = spherical.radius;
        spherical.radius = clampDistance(spherical.radius * scale);
        zoomChanged = prevRadius != spherical.radius;

        offset.setFromSpherical(spherical);

        // rotate offset back to "camera-up-vector-is-up" space
        offset.applyQuaternion(quatInverse);

        switch (scope.mode) {
          case KsgMode.Star:
            position.copy(scope.target).add(offset);
            scope.object.lookAt(scope.target);
            break;
          case KsgMode.Domain:
            offset.setLength(cmap(dollyOffset, [-200, 200], [-1, 1]));
            scope.rootAreaObj.position.add(offset.negate());
            // scope.object.lookAt(scope.target);
            break;
        }

        if (scope.enableDamping === true) {
          dollyOffset *= 1 - scope.dampingFactor;
          sphericalDelta.theta *= 1 - scope.dampingFactor;
          sphericalDelta.phi *= 1 - scope.dampingFactor;
          panOffset.multiplyScalar(1 - scope.dampingFactor);
        } else {
          dollyOffset = 0;
          sphericalDelta.set(0, 0, 0);
          panOffset.set(0, 0, 0);
        }

        scale = 1;
        // update condition is:
        // min(camera displacement, camera rotation in radians)^2 > EPS
        // using small-angle approximation cos(x/2) = 1 - x^2 / 8

        // 限制相机 y 轴位置
        switch (scope.mode) {
          case KsgMode.Star:
            scope.object.position.y = Math.min(
              Math.max(scope.object.position.y, scope.yMinRange),
              scope.yMaxRange
            );
            break;
          case KsgMode.Domain:
            break;
        }

        if (
          zoomChanged ||
          lastPosition.distanceToSquared(scope.object.position) > EPS ||
          8 * (1 - lastQuaternion.dot(scope.object.quaternion)) > EPS ||
          lastTargetPosition.distanceToSquared(scope.target) > EPS
        ) {
          scope.dispatchEvent(_changeEvent);
          lastPosition.copy(scope.object.position);
          lastQuaternion.copy(scope.object.quaternion);
          lastTargetPosition.copy(scope.target);
          return true;
        }
        return false;
      };
    })();

    this.dispose = function () {
      scope.domElement.removeEventListener("contextmenu", onContextMenu);
      scope.domElement.removeEventListener("pointerdown", onPointerDown);
      scope.domElement.removeEventListener("pointercancel", onPointerUp);
      scope.domElement.removeEventListener("wheel", onMouseWheel);
      scope.domElement.removeEventListener("pointermove", onPointerMove);
      scope.domElement.removeEventListener("pointerup", onPointerUp);
      const document = scope.domElement.getRootNode(); // offscreen canvas compatibility
      document.removeEventListener("keydown", interceptControlDown, {
        capture: true,
      });
      if (scope._domElementKeyEvents !== null) {
        scope._domElementKeyEvents.removeEventListener("keydown", onKeyDown);
        scope._domElementKeyEvents = null;
      }
    };

    this.changeMode = function (mode) {
      scope.mode = mode;
      sphericalDelta.set(0, 0, 0);
      panOffset.set(0, 0, 0);
      dollyOffset = 0;
    };

    const scope = this;
    const STATE = {
      NONE: -1,
      ROTATE: 0,
      DOLLY: 1,
      PAN: 2,
      TOUCH_ROTATE: 3,
      TOUCH_PAN: 4,
      TOUCH_DOLLY_PAN: 5,
      TOUCH_DOLLY_ROTATE: 6,
    };

    let state = STATE.NONE;
    let scale = 1;
    let controlActive = false;
    let dollyOffset = 0;
    const EPS = 0.000001;
    const panOffset = new Vector3();
    const rotateStart = new Vector2();
    const rotateEnd = new Vector2();
    const rotateDelta = new Vector2();
    const panStart = new Vector2();
    const panEnd = new Vector2();
    const panDelta = new Vector2();
    const dollyStart = new Vector2();
    const dollyEnd = new Vector2();
    const dollyDelta = new Vector2();
    const dollyDirection = new Vector3();

    // current position in spherical coordinates
    const spherical = new Spherical();
    const sphericalDelta = new Spherical();

    const pointers: any[] = [];
    const pointerPositions: { [key: number]: Vector2 } = {};

    function getZoomScale(delta: number) {
      const normalizedDelta = Math.abs(delta * 0.01);
      return Math.pow(0.95, scope.zoomSpeed * normalizedDelta);
    }

    function rotateLeft(angle: number) {
      sphericalDelta.theta -= angle;
    }

    function rotateUp(angle: number) {
      sphericalDelta.phi -= angle;
    }

    const panLeft = (function () {
      const v = new Vector3();
      return function panLeft(distance: number, objectMatrix: Matrix4) {
        v.setFromMatrixColumn(objectMatrix, 0); // get X column of objectMatrix
        v.multiplyScalar(-distance);
        panOffset.add(v);
      };
    })();

    const panUp = (function () {
      const v = new Vector3();
      return function panUp(distance: number, objectMatrix: Matrix4) {
        v.setFromMatrixColumn(objectMatrix, 1);
        v.multiplyScalar(distance);
        panOffset.add(v);
      };
    })();

    // deltaX and deltaY are in pixels; right and down are positive
    const pan = (function () {
      const offset = new Vector3();
      return function pan(deltaX: number, deltaY: number) {
        const element = scope.domElement;
        const position = scope.object.position;
        switch (scope.mode) {
          case KsgMode.Star:
            offset.copy(position).sub(scope.target);
            break;
          case KsgMode.Domain:
            deltaX = -deltaX;
            deltaY = -deltaY;
            offset.copy(position).sub(scope.rootAreaObj.position);
            break;
        }

        let targetDistance = offset.length();
        // half of the fov is center to top of screen
        targetDistance *= Math.tan(((scope.object.fov / 2) * Math.PI) / 180.0);
        // we use only clientHeight here so aspect ratio does not distort speed
        panLeft(
          (2 * deltaX * targetDistance) / element.clientHeight,
          scope.object.matrix
        );
        panUp(
          (2 * deltaY * targetDistance) / element.clientHeight,
          scope.object.matrix
        );
      };
    })();

    function dollyOut(dollyScale: number) {
      scale /= dollyScale;
    }

    function dollyIn(dollyScale: number) {
      scale *= dollyScale;
    }

    function clampDistance(dist: number) {
      return Math.max(scope.minDistance, Math.min(scope.maxDistance, dist));
    }

    function handleMouseDownRotate(event: MouseEvent) {
      rotateStart.set(event.clientX, event.clientY);
    }

    function handleMouseDownDolly(event: MouseEvent) {
      dollyStart.set(event.clientX, event.clientY);
    }

    function handleMouseDownPan(event: MouseEvent) {
      panStart.set(event.clientX, event.clientY);
    }

    function handleMouseMoveRotate(event: MouseEvent) {
      rotateEnd.set(event.clientX, event.clientY);
      rotateDelta
        .subVectors(rotateEnd, rotateStart)
        .multiplyScalar(scope.rotateSpeed);
      const element = scope.domElement;
      rotateLeft((2 * Math.PI * rotateDelta.x) / element.clientHeight); // yes, height
      rotateUp((2 * Math.PI * rotateDelta.y) / element.clientHeight);
      rotateStart.copy(rotateEnd);
      scope.update();
    }

    function handleMouseMoveDolly(event: MouseEvent) {
      dollyEnd.set(event.clientX, event.clientY);
      dollyDelta.subVectors(dollyEnd, dollyStart);
      if (dollyDelta.y > 0) {
        dollyOut(getZoomScale(dollyDelta.y));
      } else if (dollyDelta.y < 0) {
        dollyIn(getZoomScale(dollyDelta.y));
      }
      dollyOffset += dollyDelta.y;
      dollyStart.copy(dollyEnd);
      scope.update();
    }

    function handleMouseMovePan(event: MouseEvent) {
      panEnd.set(event.clientX, event.clientY);
      panDelta.subVectors(panEnd, panStart).multiplyScalar(scope.panSpeed);
      pan(panDelta.x, panDelta.y);
      panStart.copy(panEnd);
      scope.update();
    }

    function handleMouseWheel(event: any) {
      if (event.deltaY < 0) {
        dollyIn(getZoomScale(event.deltaY));
      } else if (event.deltaY > 0) {
        dollyOut(getZoomScale(event.deltaY));
      }
      dollyOffset += event.deltaY;
      scope.update();
    }

    function handleKeyDown(event: {
      code: any;
      ctrlKey: any;
      metaKey: any;
      shiftKey: any;
      preventDefault: () => void;
    }) {
      let needsUpdate = false;

      switch (event.code) {
        case scope.keys.UP:
          if (event.ctrlKey || event.metaKey || event.shiftKey) {
            rotateUp(
              (2 * Math.PI * scope.rotateSpeed) / scope.domElement.clientHeight
            );
          } else {
            pan(0, scope.keyPanSpeed);
          }

          needsUpdate = true;
          break;

        case scope.keys.BOTTOM:
          if (event.ctrlKey || event.metaKey || event.shiftKey) {
            rotateUp(
              (-2 * Math.PI * scope.rotateSpeed) / scope.domElement.clientHeight
            );
          } else {
            pan(0, -scope.keyPanSpeed);
          }

          needsUpdate = true;
          break;

        case scope.keys.LEFT:
          if (event.ctrlKey || event.metaKey || event.shiftKey) {
            rotateLeft(
              (2 * Math.PI * scope.rotateSpeed) / scope.domElement.clientHeight
            );
          } else {
            pan(scope.keyPanSpeed, 0);
          }

          needsUpdate = true;
          break;

        case scope.keys.RIGHT:
          if (event.ctrlKey || event.metaKey || event.shiftKey) {
            rotateLeft(
              (-2 * Math.PI * scope.rotateSpeed) / scope.domElement.clientHeight
            );
          } else {
            pan(-scope.keyPanSpeed, 0);
          }

          needsUpdate = true;
          break;
      }

      if (needsUpdate) {
        // prevent the browser from scrolling on cursor keys
        event.preventDefault();

        scope.update();
      }
    }

    function handleTouchStartRotate(event: PointerEvent) {
      if (pointers.length === 1) {
        rotateStart.set(event.pageX, event.pageY);
      } else {
        const position = getSecondPointerPosition(event);

        const x = 0.5 * (event.pageX + position.x);
        const y = 0.5 * (event.pageY + position.y);

        rotateStart.set(x, y);
      }
    }

    function handleTouchStartPan(event: PointerEvent) {
      if (pointers.length === 1) {
        panStart.set(event.pageX, event.pageY);
      } else {
        const position = getSecondPointerPosition(event);

        const x = 0.5 * (event.pageX + position.x);
        const y = 0.5 * (event.pageY + position.y);

        panStart.set(x, y);
      }
    }

    function handleTouchStartDolly(event: PointerEvent) {
      const position = getSecondPointerPosition(event);

      const dx = event.pageX - position.x;
      const dy = event.pageY - position.y;

      const distance = Math.sqrt(dx * dx + dy * dy);

      dollyStart.set(0, distance);
    }

    function handleTouchStartDollyPan(event: any) {
      if (scope.enableZoom) handleTouchStartDolly(event);

      if (scope.enablePan) handleTouchStartPan(event);
    }

    function handleTouchStartDollyRotate(event: any) {
      if (scope.enableZoom) handleTouchStartDolly(event);

      if (scope.enableRotate) handleTouchStartRotate(event);
    }

    function handleTouchMoveRotate(event: PointerEvent) {
      if (pointers.length == 1) {
        rotateEnd.set(event.pageX, event.pageY);
      } else {
        const position = getSecondPointerPosition(event);

        const x = 0.5 * (event.pageX + position.x);
        const y = 0.5 * (event.pageY + position.y);

        rotateEnd.set(x, y);
      }

      rotateDelta
        .subVectors(rotateEnd, rotateStart)
        .multiplyScalar(scope.rotateSpeed);

      const element = scope.domElement;

      rotateLeft((2 * Math.PI * rotateDelta.x) / element.clientHeight); // yes, height

      rotateUp((2 * Math.PI * rotateDelta.y) / element.clientHeight);

      rotateStart.copy(rotateEnd);
    }

    function handleTouchMovePan(event: PointerEvent) {
      if (pointers.length === 1) {
        panEnd.set(event.pageX, event.pageY);
      } else {
        const position = getSecondPointerPosition(event);

        const x = 0.5 * (event.pageX + position.x);
        const y = 0.5 * (event.pageY + position.y);

        panEnd.set(x, y);
      }

      panDelta.subVectors(panEnd, panStart).multiplyScalar(scope.panSpeed);

      pan(panDelta.x, panDelta.y);

      panStart.copy(panEnd);
    }

    function handleTouchMoveDolly(event: PointerEvent) {
      const position = getSecondPointerPosition(event);

      const dx = event.pageX - position.x;
      const dy = event.pageY - position.y;

      const distance = Math.sqrt(dx * dx + dy * dy);

      dollyEnd.set(0, distance);

      dollyDelta.set(0, Math.pow(dollyEnd.y / dollyStart.y, scope.zoomSpeed));

      dollyOut(dollyDelta.y);

      dollyStart.copy(dollyEnd);

      const centerX = (event.pageX + position.x) * 0.5;
      const centerY = (event.pageY + position.y) * 0.5;
    }

    function handleTouchMoveDollyPan(event: PointerEvent) {
      if (scope.enableZoom) handleTouchMoveDolly(event);
      if (scope.enablePan) handleTouchMovePan(event);
    }

    function handleTouchMoveDollyRotate(event: PointerEvent) {
      if (scope.enableZoom) handleTouchMoveDolly(event);
      if (scope.enableRotate) handleTouchMoveRotate(event);
    }

    function onPointerDown(event: PointerEvent) {
      if (scope.enabled === false) return;
      if (pointers.length === 0) {
        scope.domElement.setPointerCapture(event.pointerId);
        scope.domElement.addEventListener("pointermove", onPointerMove);
        scope.domElement.addEventListener("pointerup", onPointerUp);
      }

      if (isTrackingPointer(event)) return;

      addPointer(event);

      if (event.pointerType === "touch") {
        onTouchStart(event);
      } else {
        onMouseDown(event);
      }
    }

    function onPointerMove(event: PointerEvent) {
      if (scope.enabled === false) return;
      changeControlsStatus();
      if (event.pointerType === "touch") {
        onTouchMove(event);
      } else {
        onMouseMove(event);
      }
    }

    /**
     * 修改控制状态
     */
    function changeControlsStatus() {
      if (scope.controlsTimer) clearTimeout(scope.controlsTimer as number);
      scope.isControls = true;
      scope.controlsTimer = setTimeout(() => {
        scope.isControls = false;
      }, 300);
    }

    function onPointerUp(event: PointerEvent) {
      removePointer(event);
      switch (pointers.length) {
        case 0:
          scope.domElement.releasePointerCapture(event.pointerId);
          scope.domElement.removeEventListener("pointermove", onPointerMove);
          scope.domElement.removeEventListener("pointerup", onPointerUp);
          scope.dispatchEvent(_endEvent);
          state = STATE.NONE;
          break;
        case 1:
          const pointerId = pointers[0];
          const position = pointerPositions[pointerId];
          // minimal placeholder event - allows state correction on pointer-up
          onTouchStart({
            pointerId: pointerId,
            pageX: position.x,
            pageY: position.y,
          } as PointerEvent);
          break;
      }
    }

    function onMouseDown(event: MouseEvent) {
      let mouseAction;
      switch (event.button) {
        case 0:
          mouseAction = scope.mouseButtons.LEFT;
          break;
        case 1:
          mouseAction = scope.mouseButtons.MIDDLE;
          break;
        case 2:
          mouseAction = scope.mouseButtons.RIGHT;
          break;
        default:
          mouseAction = -1;
      }

      switch (mouseAction) {
        case MOUSE.DOLLY:
          if (scope.enableZoom === false) return;
          handleMouseDownDolly(event);
          state = STATE.DOLLY;
          break;
        case MOUSE.ROTATE:
          if (event.ctrlKey || event.metaKey || event.shiftKey) {
            if (scope.enablePan === false) return;
            handleMouseDownPan(event);
            state = STATE.PAN;
          } else {
            if (scope.enableRotate === false) return;
            handleMouseDownRotate(event);
            state = STATE.ROTATE;
          }
          break;
        case MOUSE.PAN:
          if (event.ctrlKey || event.metaKey || event.shiftKey) {
            if (scope.enableRotate === false) return;
            handleMouseDownRotate(event);
            state = STATE.ROTATE;
          } else {
            if (scope.enablePan === false) return;
            handleMouseDownPan(event);
            state = STATE.PAN;
          }
          break;
        default:
          state = STATE.NONE;
      }
      if (state !== STATE.NONE) {
        scope.dispatchEvent(_startEvent);
      }
    }

    function onMouseMove(event: MouseEvent) {
      switch (state) {
        case STATE.ROTATE:
          if (scope.enableRotate === false) return;
          handleMouseMoveRotate(event);
          break;
        case STATE.DOLLY:
          if (scope.enableZoom === false) return;
          handleMouseMoveDolly(event);
          break;
        case STATE.PAN:
          if (scope.enablePan === false) return;
          handleMouseMovePan(event);
          break;
      }
    }

    function onMouseWheel(event: WheelEvent) {
      if (
        scope.enabled === false ||
        scope.enableZoom === false ||
        state !== STATE.NONE
      )
        return;
      event.preventDefault();
      scope.dispatchEvent(_startEvent);
      handleMouseWheel(customWheelEvent(event));
      scope.dispatchEvent(_endEvent);
    }

    function customWheelEvent(event: WheelEvent) {
      const mode = event.deltaMode;
      // minimal wheel event altered to meet delta-zoom demand
      const newEvent = {
        clientX: event.clientX,
        clientY: event.clientY,
        deltaY: event.deltaY,
      };
      switch (mode) {
        case WheelEvent.DOM_DELTA_LINE: // LINE_MODE
          newEvent.deltaY *= 16;
          break;
        case WheelEvent.DOM_DELTA_PAGE: // PAGE_MODE
          newEvent.deltaY *= 100;
          break;
      }
      // detect if event was triggered by pinching
      if (event.ctrlKey && !controlActive) {
        newEvent.deltaY *= 10;
      }
      return newEvent;
    }

    function interceptControlDown(event: Event) {
      const e = event as KeyboardEvent;
      if (e.key === "Control") {
        controlActive = true;
        const document = scope.domElement.getRootNode(); // offscreen canvas compatibility
        document.addEventListener("keyup", interceptControlUp, {
          passive: true,
          capture: true,
        });
      }
    }

    function interceptControlUp(event: Event) {
      const e = event as KeyboardEvent;
      if (e.key === "Control") {
        controlActive = false;
        const document = scope.domElement.getRootNode(); // offscreen canvas compatibility
        document.removeEventListener("keyup", interceptControlUp, {
          capture: true,
        });
      }
    }

    function onKeyDown(event: KeyboardEvent) {
      if (scope.enabled === false || scope.enablePan === false) return;
      handleKeyDown(event);
    }

    function onTouchStart(event: PointerEvent) {
      trackPointer(event);
      switch (pointers.length) {
        case 1:
          switch (scope.touches.ONE) {
            case TOUCH.ROTATE:
              if (scope.enableRotate === false) return;
              handleTouchStartRotate(event);
              state = STATE.TOUCH_ROTATE;
              break;
            case TOUCH.PAN:
              if (scope.enablePan === false) return;
              handleTouchStartPan(event);
              state = STATE.TOUCH_PAN;
              break;
            default:
              state = STATE.NONE;
          }
          break;
        case 2:
          switch (scope.touches.TWO) {
            case TOUCH.DOLLY_PAN:
              if (scope.enableZoom === false && scope.enablePan === false)
                return;
              handleTouchStartDollyPan(event);
              state = STATE.TOUCH_DOLLY_PAN;
              break;
            case TOUCH.DOLLY_ROTATE:
              if (scope.enableZoom === false && scope.enableRotate === false)
                return;
              handleTouchStartDollyRotate(event);
              state = STATE.TOUCH_DOLLY_ROTATE;
              break;
            default:
              state = STATE.NONE;
          }
          break;
        default:
          state = STATE.NONE;
      }

      if (state !== STATE.NONE) {
        scope.dispatchEvent(_startEvent);
      }
    }

    function onTouchMove(event: PointerEvent) {
      trackPointer(event);

      switch (state) {
        case STATE.TOUCH_ROTATE:
          if (scope.enableRotate === false) return;

          handleTouchMoveRotate(event);

          scope.update();

          break;

        case STATE.TOUCH_PAN:
          if (scope.enablePan === false) return;

          handleTouchMovePan(event);

          scope.update();

          break;

        case STATE.TOUCH_DOLLY_PAN:
          if (scope.enableZoom === false && scope.enablePan === false) return;

          handleTouchMoveDollyPan(event);

          scope.update();

          break;

        case STATE.TOUCH_DOLLY_ROTATE:
          if (scope.enableZoom === false && scope.enableRotate === false)
            return;

          handleTouchMoveDollyRotate(event);

          scope.update();

          break;

        default:
          state = STATE.NONE;
      }
    }

    function onContextMenu(event: MouseEvent) {
      if (scope.enabled === false) return;
      event.preventDefault();
    }

    function addPointer(event: PointerEvent) {
      pointers.push(event.pointerId);
    }

    function removePointer(event: PointerEvent) {
      delete pointerPositions[event.pointerId];
      for (let i = 0; i < pointers.length; i++) {
        if (pointers[i] == event.pointerId) {
          pointers.splice(i, 1);
          return;
        }
      }
    }

    function isTrackingPointer(event: PointerEvent) {
      for (let i = 0; i < pointers.length; i++) {
        if (pointers[i] == event.pointerId) return true;
      }
      return false;
    }

    function trackPointer(event: PointerEvent) {
      let position = pointerPositions[event.pointerId];
      if (position === undefined) {
        position = new Vector2();
        pointerPositions[event.pointerId] = position;
      }
      position.set(event.pageX, event.pageY);
    }

    function getSecondPointerPosition(event: PointerEvent) {
      const pointerId =
        event.pointerId === pointers[0] ? pointers[1] : pointers[0];
      return pointerPositions[pointerId];
    }

    scope.domElement.addEventListener("contextmenu", onContextMenu);
    scope.domElement.addEventListener("pointerdown", onPointerDown);
    scope.domElement.addEventListener("pointercancel", onPointerUp);
    scope.domElement.addEventListener("wheel", onMouseWheel, {
      passive: false,
    });
    const document = scope.domElement.getRootNode(); // offscreen canvas compatibility
    document.addEventListener("keydown", interceptControlDown, {
      passive: true,
      capture: true,
    });
  }
  /**
   * 自动旋转函数,若要开启自动旋转功能,请设置autoRotate为true
   * 渲染帧中调用此方法
   */
  autoRotateUpdate(deltaTime: number) {
    //cameral auto rotate around
    if (this.autoRotate) {
      const offset1 = this.object.position.clone().sub(this.target);
      const angle = this.autoRotateSpeed * deltaTime!;
      const rotationMatrix = new Matrix4().makeRotationY(angle);
      offset1.applyMatrix4(rotationMatrix);
      this.object.position.copy(this.target).add(offset1);
      this.object.lookAt(this.target);
    }
  }
}

export { KsgMode, KsgControls };
