import { InstancedMesh } from "three";
import { Point } from "../types";
import { Context } from "../ctx";
declare class KsgHoverCircle extends InstancedMesh {
    lastIndex: number;
    constructor(size?: number);
    /**
     *把该模型绑定到hover节点
     *@param {KsgPoint} bindPoint 要绑定的节点的userData
     */
    display(bindPoint: Point): void;
    /**
     *移出hover状态
     */
    hide(): void;
    /**
     *动画更新函数
     */
    update(ctx: Partial<Context>, delta?: number): void;
    /**
     * 释放内存
     */
    free(): void;
}
declare const ksgHover: KsgHoverCircle;
export default ksgHover;
