# KsgMap 交互功能详解

## 概述

KsgMap提供了丰富的交互功能，包括：
1. **Label显示系统** - 智能标签显示机制
2. **自动旋转功能** - 全局视图下的自动相机旋转
3. **交互控制系统** - 统一的用户交互管理

## 一、Label显示机制

KsgMap中的Label显示系统是一个基于Three.js CSS2DRenderer的智能标签系统，支持两种主要的显示模式：
1. **Hover Label** - 鼠标悬停时临时显示的标签
2. **Focus Label** - 点击节点后持续显示的聚焦标签

## 核心架构

### 1. 标签类结构

```typescript
export class KsgLabel extends CSS2DObject {
  labelWidth: number = 0;      // 标签宽度
  labelHeight: number = 0;     // 标签高度  
  point: Point | null = null;  // 当前绑定的节点
  lastIndex: number | null = null; // 上次显示的节点索引
  offset: { x: number; y: number }; // 标签偏移位置
}
```

### 2. 全局标签实例

系统创建了两个全局标签实例：
- `hoverLabel` - 悬停标签实例
- `focusLabel` - 聚焦标签实例

## Hover显示机制

### 实现流程

```mermaid
sequenceDiagram
    participant User as 用户
    participant Event as 事件系统
    participant Raycaster as 射线投射
    participant HoverLabel as 悬停标签
    participant Animation as 动画系统

    User->>Event: 鼠标移动
    Event->>Raycaster: 坐标转换与射线检测
    Raycaster->>Event: 返回相交节点
    Event->>HoverLabel: 调用display()显示标签
    HoverLabel->>Animation: 执行进入动画
    Animation-->>User: 标签淡入显示
    
    User->>Event: 鼠标移出
    Event->>HoverLabel: 调用hide()隐藏标签
    HoverLabel->>Animation: 执行离开动画
    Animation-->>User: 标签淡出隐藏
```

### 核心代码实现

#### 1. 悬停事件检测 (`hoverObjectEvent.ts`)

<augment_code_snippet path="KsgMap/src/components/ksgMap/utils/hoverObjectEvent.ts" mode="EXCERPT">
````typescript
function event(e: MouseEvent) {
  // 检查点网格是否存在，以及控制器是否正在操作
  if (!ctx.pointsMesh || ctx.controls?.isControls) return;
  
  const { offsetX, offsetY } = e;
  
  // 将屏幕坐标转换为标准化设备坐标(-1到1)
  raycaster!.setFromCamera(
    new THREE.Vector2(
      (offsetX / width!) * 2 - 1,
      -(offsetY / height!) * 2 + 1
    ),
    ctx.camera!
  );
  
  // 检测与点网格的交集
  const intersects = raycaster!.intersectObject(ctx.pointsMesh);
````
</augment_code_snippet>

#### 2. 悬停回调处理 (`event.ts`)

<augment_code_snippet path="KsgMap/src/components/ksgMap/config/event.ts" mode="EXCERPT">
````typescript
// 鼠标悬停到节点时的回调函数
(point: Point) => {
  // 获取节点在屏幕上的2D坐标
  const pointDnc = ctx.pointsMesh?.getWorldP(
    point.index!,
    ctx.camera!,
    wrapperElSize.width * window.devicePixelRatio,
    wrapperElSize.height * window.devicePixelRatio
  )!;

  // 显示悬停光圈效果
  ksgHover.display(point);

  // 显示悬停标签
  hoverLabel.display(point, {
    viewRange: ctx.viewRange!,
    dnc: pointDnc,
  });

  // 改变鼠标样式为指针
  document.body.style.cursor = "pointer";
}
````
</augment_code_snippet>

#### 3. 标签显示逻辑 (`KsgLabel.ts`)

<augment_code_snippet path="KsgMap/src/components/ksgMap/core/KsgLabel.ts" mode="EXCERPT">
````typescript
display(point: Point, option?: { viewRange: ViewRange; dnc: { x: number; y: number } }) {
  // 避免重复显示同一个节点
  if (this.lastIndex === point.index) return;
  
  // 绑定节点数据
  this.point = point;
  
  // 设置标签HTML内容
  this.element.innerHTML = `<div class='css2d-label-inner'>${reFormateTitle(point.name)}</div>`;
  
  // 设置3D位置
  this.position.set(...point.coordinate);
  
  // 渲染MathJax数学公式
  renderMathJax(this.element);
  
  // 计算并设置最佳显示位置
  if (option) {
    this.setPosition(option);
  }
  
  this.visible = true;
  this.lastIndex = point.index;
}
````
</augment_code_snippet>

## 点击持续显示机制

### 实现流程

```mermaid
sequenceDiagram
    participant User as 用户
    participant ClickEvent as 点击事件
    participant FocusSystem as 聚焦系统
    participant FocusLabel as 聚焦标签
    participant Animation as 动画系统

    User->>ClickEvent: 点击节点
    ClickEvent->>FocusSystem: 调用handleEnterFocus()
    FocusSystem->>FocusSystem: 构建聚焦数据
    FocusSystem->>Animation: 执行相机移动动画
    Animation->>FocusLabel: 显示聚焦标签
    FocusLabel-->>User: 标签持续显示
    
    Note over FocusLabel: 标签会一直显示直到：<br/>1. 点击其他节点<br/>2. 退出聚焦模式<br/>3. 进入全局视图
```

### 核心代码实现

#### 1. 点击事件检测 (`clickPointEvent.ts`)

<augment_code_snippet path="KsgMap/src/components/ksgMap/utils/clickPointEvent.ts" mode="EXCERPT">
````typescript
function event(e: MouseEvent) {
  if (!ctx.pointsMesh) return;
  
  const { offsetX, clientX, offsetY, clientY, button } = e;
  
  // 如果点击的是标签或者不是左键点击，则忽略
  if (isClickedLabel(clientX, clientY) || button !== MOUSE.LEFT) return;
  
  // 射线检测
  raycaster!.setFromCamera(
    new Vector2((offsetX / width!) * 2 - 1, -(offsetY / height!) * 2 + 1),
    ctx.camera!
  );
  
  const intersects = raycaster!.intersectObject(ctx.pointsMesh);
  
  if (intersects[0]?.object && intersects[0].distance <= ctx.maxDistance!) {
    const id = (intersects[0].object as KsgPoint).getPointData(fistPointIndex!)!.id;
    clickedPointCallback(ctx.graph?.getPointById(id)!);
  }
}
````
</augment_code_snippet>

#### 2. 聚焦处理逻辑 (`event.ts`)

<augment_code_snippet path="KsgMap/src/components/ksgMap/config/event.ts" mode="EXCERPT">
````typescript
// 点击节点进入聚焦模式的回调
(point: Point) => {
  // 隐藏悬停相关的UI元素
  enableHover = false;
  ksgHover.hide();
  hoverLabel.hide();
  focusLabel.hide();
  
  // 清空之前的动画
  TWEEN.removeAll();
  
  // 显示聚焦外壳效果
  focusCrust.display(point);
  
  // 切换节点为聚焦状态
  ctx.pointsMesh?.toggleFocus(point.index!);
  
  // 执行聚焦处理
  handleEnterFocus(point).then(() => {
    enableHover = true;
  });
}
````
</augment_code_snippet>

#### 3. 聚焦标签显示 (`renderData.ts`)

<augment_code_snippet path="KsgMap/src/components/ksgMap/core/renderData.ts" mode="EXCERPT">
````typescript
export function renderFocusData(focusInfo: FocusData): Promise<void> {
  // 显示聚焦外壳效果
  focusCrust.display(focusInfo.focusPoint);
  ctx.pointsMesh?.toggleFocus(focusInfo.focusPoint.index!);
  
  // 执行视角移动动画
  return viewMoveAnimation(focusInfo.focusPoint.coordinate, ctx.camera!)
    .then(() =>
      // 视角移动完成后显示聚焦标签
      focusLabel.display(focusInfo.focusPoint)
    )
    .then(() => {
      // 重新启用控制器
      ctx.controls!.enabled = true;
      // ... 其他处理逻辑
    });
}
````
</augment_code_snippet>

## 标签动画系统

### 动画类型

1. **进入动画** (`labelEnter`)
   - 透明度：0 → 1
   - 位置：Y轴向上偏移0.4单位后回到原位
   - 持续时间：150ms

2. **离开动画** (`labelLeave`)  
   - 透明度：1 → 0
   - 位置：Y轴向下偏移0.4单位
   - 持续时间：150ms

### 动画实现

<augment_code_snippet path="KsgMap/src/components/ksgMap/animation/label.ts" mode="EXCERPT">
````typescript
// hover时显示label动画
export function labelEnter(labelObject: CSS2DObject, duration: number = 150) {
  labelObject.element.style.opacity = "0";
  labelObject.position.set(0, 0.4, 0);
  
  return new Promise((resolve) => {
    new TWEEN.Tween({ x: 0, y: 0.4, z: 0, opacity: 0 })
      .to({ x: 0, y: 0, z: 0, opacity: 1 }, duration)
      .onUpdate(({ x, y, z, opacity }) => {
        labelObject.position.set(x, y, z);
        labelObject.element.style.opacity = opacity.toString();
      })
      .start();
  });
}
````
</augment_code_snippet>

## 智能定位算法

### 边界检测与位置优化

标签系统具备智能定位功能，能够：
1. 检测视口边界，避免标签溢出
2. 动态调整标签位置和锚点
3. 考虑设备像素比进行精确定位

### 距离控制显示

<augment_code_snippet path="KsgMap/src/components/ksgMap/core/KsgLabel.ts" mode="EXCERPT">
````typescript
// 根据相机距离控制标签显示
distanceShow(show: boolean) {
  this.element.style.setProperty(
    "--animation",
    show ? "label-enter" : "label-leave"
  );
}
````
</augment_code_snippet>

## 三、总结

KsgMap的交互功能系统通过以下关键技术实现：

### Label显示机制
1. **射线投射检测** - 精确识别用户交互的节点
2. **双标签系统** - 分离悬停和聚焦状态的标签管理
3. **智能定位算法** - 确保标签始终在可视范围内
4. **平滑动画过渡** - 提供良好的用户体验
5. **状态管理** - 协调不同交互模式下的标签显示

### 自动旋转功能
1. **基于时间的旋转算法** - 使用deltaTime确保平滑旋转
2. **矩阵变换计算** - 通过Y轴旋转矩阵实现相机轨道运动
3. **智能交互检测** - 防抖机制检测用户活动并自动暂停/恢复
4. **渲染循环集成** - 与渲染系统无缝集成，确保性能
5. **灵活配置系统** - 支持速度、延时等多种参数调整

### 技术优势
- **性能优化** - 只在需要时进行计算，避免不必要的资源消耗
- **用户体验** - 智能的交互响应，不干扰用户操作
- **可扩展性** - 模块化设计，易于扩展新功能
- **稳定性** - 完善的状态管理和错误处理机制

这套完整的交互系统确保了用户在浏览知识图谱时能够获得直观、流畅、沉浸式的体验。

## 二、自动旋转功能

### 概述

自动旋转功能是KsgMap在全局视图模式下提供的一个重要特性，它能让相机围绕知识图谱自动旋转，为用户提供360度的全景浏览体验。该功能具备智能的用户交互检测，当用户操作时自动暂停，操作结束后自动恢复。

### 核心实现原理

#### 1. 自动旋转算法

自动旋转基于Three.js的矩阵变换实现，核心算法位于`KsgControls.ts`中：

<augment_code_snippet path="KsgMap/src/components/ksgMap/core/KsgControls.ts" mode="EXCERPT">
````typescript
/**
 * 自动旋转函数,若要开启自动旋转功能,请设置autoRotate为true
 * 渲染帧中调用此方法
 */
autoRotateUpdate(deltaTime: number) {
  if (this.autoRotate) {
    // 计算相机相对于目标的偏移向量
    const offset1 = this.object.position.clone().sub(this.target);

    // 计算旋转角度（基于时间和速度）
    const angle = this.autoRotateSpeed * deltaTime;

    // 创建绕Y轴旋转的矩阵
    const rotationMatrix = new Matrix4().makeRotationY(angle);

    // 应用旋转变换
    offset1.applyMatrix4(rotationMatrix);

    // 更新相机位置并保持朝向目标
    this.object.position.copy(this.target).add(offset1);
    this.object.lookAt(this.target);
  }
}
````
</augment_code_snippet>

#### 2. 渲染循环集成

自动旋转在每个渲染帧中被调用，确保平滑的旋转效果：

<augment_code_snippet path="KsgMap/src/components/ksgMap/hooks/useRendererFrame.ts" mode="EXCERPT">
````typescript
function startRenderFrame(time: any = 0) {
  const deltaTime = clock.getDelta();

  // CSS2D 渲染器 - 渲染知识点标签等 2D 元素
  ctx.css2dRenderer?.render(ctx.scene!, ctx.camera!);

  // 主 WebGL 渲染器 - 渲染 3D 场景
  ctx.renderer?.render(ctx.scene!, ctx.camera!);

  // 相机控制器更新 - 处理用户交互
  ctx.controls?.update(deltaTime);
  // 自动旋转更新（如果启用）
  ctx.controls?.autoRotateUpdate(deltaTime);

  // 动画系统更新
  TWEEN.update(time);

  // 递归调用下一帧
  requestAnimationFrame(startRenderFrame);
}
````
</augment_code_snippet>

### 智能交互控制

#### 1. 鼠标移动检测

系统通过防抖机制检测用户的鼠标活动：

<augment_code_snippet path="KsgMap/src/components/ksgMap/utils/globalViewEvent.ts" mode="EXCERPT">
````typescript
export function createMouseMoveEvent(
  onMove: () => void,
  onMoveEnd: () => void,
  moveEndDelay: number = 100
) {
  let timer: number | null | NodeJS.Timer = null;

  function handleMoveEvent(event: MouseEvent) {
    // 首次移动立即触发onMove
    if (!timer) onMove();

    // 清除之前的定时器，重新开始计时
    clearTimeout(timer as number);

    // 设置新的定时器，在指定延迟后触发移动结束事件
    timer = setTimeout(() => {
      onMoveEnd();
      timer = null;
    }, moveEndDelay);
  }

  return { handleMoveEvent, clear };
}
````
</augment_code_snippet>

#### 2. 自动旋转启停控制

基于用户交互状态智能控制自动旋转的启停：

<augment_code_snippet path="KsgMap/src/components/ksgMap/config/event.ts" mode="EXCERPT">
````typescript
// 鼠标移动时停止自动旋转
function onMove() {
  if (ctx.viewMode === VIEW_MODE.GLOBAL_VIEW && ctx.controls!.autoRotate) {
    ctx.controls!.autoRotate = false;
  }
}

// 鼠标停止移动后恢复自动旋转
function onMoveEnd() {
  if (ctx.viewMode === VIEW_MODE.GLOBAL_VIEW && !ctx.controls!.autoRotate) {
    ctx.controls!.autoRotate = true;
  }
}

// 创建鼠标移动检测器，3秒无操作后恢复自动旋转
const { handleMoveEvent, clear: clearMoveEvent } = createMouseMoveEvent(
  onMove,
  onMoveEnd,
  3000
);
````
</augment_code_snippet>

### 配置参数

#### 1. 基础配置

<augment_code_snippet path="KsgMap/src/components/ksgMap/core/KsgControls.ts" mode="EXCERPT">
````typescript
// 初始化自动旋转功能
this.autoRotate = false;       // 默认关闭自动旋转
this.autoRotateSpeed = 2.0;    // 自动旋转速度
````
</augment_code_snippet>

#### 2. 运行时配置

| 参数名 | 默认值 | 说明 | 推荐范围 |
|--------|--------|------|----------|
| `autoRotate` | `false` | 是否启用自动旋转 | `true/false` |
| `autoRotateSpeed` | `2.0` | 旋转速度倍数 | `0.01-0.1` |
| `moveEndDelay` | `3000ms` | 鼠标无操作检测时间 | `1000-5000ms` |

### 使用方式

#### 1. 启用自动旋转

```typescript
// 方式1：通过控制器直接设置
ctx.controls!.autoRotate = true;
ctx.controls!.autoRotateSpeed = 0.02;

// 方式2：在全局视图模式下自动启用
// 双击进入全局视图时会自动启用自动旋转
```

#### 2. 动态调整速度

```typescript
// 慢速旋转 - 适合细致观察
ctx.controls!.autoRotateSpeed = 0.01;

// 标准速度 - 推荐使用
ctx.controls!.autoRotateSpeed = 0.02;

// 快速旋转 - 适合演示
ctx.controls!.autoRotateSpeed = 0.05;
```

#### 3. 临时停止/恢复

```typescript
// 临时停止自动旋转
ctx.controls!.autoRotate = false;

// 恢复自动旋转
ctx.controls!.autoRotate = true;
```

### 应用场景

1. **全局视图浏览** - 在全局视图模式下提供360度全景浏览
2. **演示展示** - 自动展示知识图谱的整体结构
3. **无人值守展示** - 在展示场景中自动循环播放
4. **用户引导** - 帮助新用户了解图谱的整体布局

### 技术特点

1. **基于时间的平滑旋转** - 使用`deltaTime`确保不同帧率下的一致体验
2. **智能交互检测** - 自动检测用户操作并暂停旋转
3. **防抖机制** - 避免频繁的启停切换
4. **性能优化** - 只在需要时进行矩阵计算
5. **可配置性** - 支持多种参数调整以适应不同需求
### 旋转时的位置变化
节点的世界坐标不会改变

从代码可以看出：
1. 节点的坐标存储在`point.coordinate`中，这是**世界坐标系**中的固定位置
2. 自动旋转只改变**相机的位置**，不触碰任何节点的坐标数据
3. 节点坐标在`KsgGraph.computePointPosition()`中计算后就固定了

### **相机与节点的距离是否改变？**
**答案：相机到target的距离不变，但到各个节点的距离会改变**

#### 距离关系分析：

1. **相机到target的距离保持不变**
   ```typescript
   // 自动旋转算法确保这个距离不变
   const offset1 = this.object.position.clone().sub(this.target);
   // offset1的长度(距离)在旋转过程中保持不变
   ```

2. **相机到各个节点的距离会改变**
   - 相机围绕target做圆周运动
   - 不同节点相对于这个圆周的位置不同
   - 因此相机到各节点的距离在旋转过程中会动态变化

### **技术实现原理**

````typescript path=KsgMap/src/components/ksgMap/core/KsgControls.ts mode=EXCERPT
autoRotateUpdate(deltaTime: number) {
  if (this.autoRotate) {
    // 1. 计算相机相对于目标的偏移向量(距离和方向)
    const offset1 = this.object.position.clone().sub(this.target);
    
    // 2. 基于时间计算旋转角度
    const angle = this.autoRotateSpeed * deltaTime;
    
    // 3. 创建绕Y轴的旋转矩阵
    const rotationMatrix = new Matrix4().makeRotationY(angle);
    
    // 4. 将偏移向量应用旋转变换(保持长度不变)
    offset1.applyMatrix4(rotationMatrix);
    
    // 5. 计算新的相机位置 = target + 旋转后的偏移
    this.object.position.copy(this.target).add(offset1);
    
    // 6. 相机始终朝向target
    this.object.lookAt(this.target);
  }
}
````

### **视觉效果的产生**

虽然节点坐标不变，但用户看到的视觉效果是动态的，原因是：

1. **屏幕投影坐标改变** - 相机位置变化导致节点在屏幕上的投影位置改变
2. **透视效果变化** - 不同角度观察产生不同的透视效果
3. **深度关系变化** - 节点的前后遮挡关系随相机角度改变

### **数学模型**

```
设：
- target = [0, 0, 0] (旋转中心)
- camera_initial = [r*cos(θ₀), y, r*sin(θ₀)]
- node = [x, y, z] (固定不变)

旋转后：
- camera_new = [r*cos(θ₀+Δθ), y, r*sin(θ₀+Δθ)]
- node = [x, y, z] (仍然不变)

距离关系：
- |camera - target| = r (不变)
- |camera - node| = √[(r*cos(θ)-x)² + (y_cam-y)² + (r*sin(θ)-z)²] (变化)
```

### **总结**

- ✅ **节点世界坐标**：完全不变
- ✅ **相机到target距离**：保持不变  
- ❌ **相机到各节点距离**：动态变化
- ✅ **视觉效果**：通过相机运动产生360度观察体验

这种设计既保证了数据的稳定性，又实现了流畅的视觉效果。
